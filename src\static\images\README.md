# 图片资源说明

## 目录结构

```
static/images/
├── logo.png              # 应用Logo
├── avatar.jpg             # 默认头像
├── work-1.jpg             # 工作照片示例1
├── work-2.jpg             # 工作照片示例2
├── doc-cover-1.jpg        # 文档封面1
├── doc-cover-2.jpg        # 文档封面2
├── doc-cover-3.jpg        # 文档封面3
├── doc-cover-4.jpg        # 文档封面4
├── doc-cover-5.jpg        # 文档封面5
├── manual-cover-1.jpg     # 手册封面1
├── manual-cover-2.jpg     # 手册封面2
└── placeholder.png        # 占位图片
```

## 图片规格要求

### Logo
- 尺寸: 512x512px
- 格式: PNG (支持透明背景)
- 用途: 应用图标、启动页

### 头像
- 尺寸: 200x200px
- 格式: JPG/PNG
- 用途: 用户头像默认图片

### 工作照片
- 尺寸: 800x600px
- 格式: JPG
- 用途: 工作记录、现场照片示例

### 文档封面
- 尺寸: 400x300px
- 格式: JPG
- 用途: 知识库文档封面图

### 手册封面
- 尺寸: 600x400px
- 格式: JPG
- 用途: 技术手册推荐内容封面

## 使用说明

1. **开发阶段**: 可以使用占位图片或在线图片服务
2. **生产环境**: 替换为实际的业务图片
3. **图片优化**: 建议使用压缩工具减小文件大小
4. **命名规范**: 使用有意义的文件名，便于维护

## 在线占位图片服务

开发阶段可以使用以下在线服务：
- https://picsum.photos/ (随机图片)
- https://placeholder.com/ (纯色占位图)
- https://via.placeholder.com/ (自定义占位图)

## 注意事项

- 确保图片版权合规
- 控制图片文件大小，避免影响应用性能
- 考虑不同屏幕密度的适配
- 为重要图片提供备用方案
