<template>
  <view class="dashboard-page">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 顶部Header区域 -->
    <view class="header-section">
      <!-- 公司品牌区域 -->
      <view class="brand-area">
        <view class="brand-logo">
          <image class="logo-image" src="/static/logo.png" mode="aspectFit" />
        </view>
        <view class="brand-info">
          <text class="brand-name">{{ companyInfo.name }}</text>
          <text class="brand-slogan">{{ companyInfo.slogan }}</text>
        </view>
        <!-- <view class="header-actions">
          <view class="action-btn" @click="handleNotification">
            <uni-icons type="notification" size="22" color="#fff" />
            <view v-if="unreadCount > 0" class="notification-badge">{{ unreadCount }}</view>
          </view>
        </view> -->
      </view>

      <!-- 用户欢迎区域 -->
      <view class="welcome-area">
        <view class="user-avatar">
          <image
            class="avatar-img"
            :src="userInfo.avatar || '/static/default-avatar.png'"
            mode="aspectFill"
          />
          <view class="online-status"></view>
        </view>
        <view class="welcome-content">
          <text class="welcome-text">{{ greeting }}，{{ userInfo.name || '用户' }}</text>
          <text class="role-badge">{{ getRoleText(userInfo.role) }}</text>
        </view>
      </view>
    </view>

    <!-- 主内容滚动区域 -->
    <scroll-view class="main-scroll" scroll-y enable-back-to-top>
      <view class="content-container">

        <!-- 数据统计卡片组 -->
        <view class="stats-section">
          <view class="section-title">
            <text class="title-text">今日概览</text>
            <text class="title-desc">实时工作数据</text>
          </view>
          <view class="stats-grid">
            <view
              class="stat-card"
              v-for="stat in statsData"
              :key="stat.id"
              @click="handleStatClick(stat)"
            >
              <!-- 卡片头部：图标和趋势 -->
              <view class="stat-header">
                <view class="stat-icon" :style="{ backgroundColor: stat.color }">
                  <uni-icons :type="stat.icon" size="20" color="#fff" />
                </view>
                <view class="stat-trend" :class="stat.trend">
                  <uni-icons :type="getTrendIcon(stat.trend)" size="12" :color="getTrendColor(stat.trend)" />
                  <text class="trend-value">{{ stat.change }}</text>
                </view>
              </view>

              <!-- 卡片主体：数据 -->
              <view class="stat-body">
                <text class="stat-number">{{ stat.value }}</text>
                <text class="stat-label">{{ stat.label }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 快速操作区域 -->
        <!-- <view class="quick-actions-section">
          <view class="section-title">
            <text class="title-text">快速操作</text>
            <text class="title-desc">常用功能入口</text>
          </view>
          <view class="actions-grid">
            <view
              class="action-item"
              v-for="action in quickActions"
              :key="action.id"
              @click="handleQuickAction(action)"
            >
              <view class="action-icon" :style="{ backgroundColor: action.color }">
                <uni-icons :type="action.icon" size="28" color="#fff" />
              </view>
              <text class="action-label">{{ action.label }}</text>
            </view>
          </view>
        </view> -->

        <!-- 今日任务区域 -->
        <view class="tasks-section">
          <view class="section-header">
            <view class="section-title">
              <text class="title-text">今日任务</text>
              <text class="title-desc">{{ todayTasks.length }} 个任务</text>
            </view>
            <view class="header-action" @click="goToTaskList">
              <text class="action-text">查看全部</text>
              <uni-icons type="right" size="16" color="#4c6ef5" />
            </view>
          </view>

          <view class="task-list">
            <view
              class="task-item"
              v-for="task in todayTasks.slice(0, 3)"
              :key="task.id"
              @click="goToTaskDetail(task)"
            >
              <view class="task-status" :class="`status-${task.status}`">
                <view class="status-dot"></view>
              </view>
              <view class="task-content">
                <text class="task-title">{{ task.title }}</text>
                <view class="task-meta">
                  <view class="task-location">
                    <uni-icons type="location" size="14" color="#999" />
                    <text class="location-text">{{ task.address }}</text>
                  </view>
                  <view class="task-time">
                    <uni-icons type="clock" size="14" color="#999" />
                    <text class="time-text">{{ task.time }}</text>
                  </view>
                </view>
              </view>
              <view class="task-priority" :class="`priority-${task.priority}`">
                <text class="priority-text">{{ getPriorityText(task.priority) }}</text>
              </view>
            </view>

            <view v-if="todayTasks.length === 0" class="empty-tasks">
              <uni-icons type="list" size="48" color="#ddd" />
              <text class="empty-text">今日暂无任务</text>
            </view>
          </view>
        </view>



      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 悬浮操作按钮 -->
    <view class="floating-action" @click="toggleFabMenu">
      <view class="fab-icon">
        <uni-icons :type="fabMenuVisible ? 'closeempty' : 'plus'" size="28" color="#fff" />
      </view>
    </view>

    <!-- 悬浮菜单 -->
    <view v-if="fabMenuVisible" class="fab-menu" @click="hideFabMenu">
      <view
        class="fab-menu-item"
        v-for="item in fabMenuItems"
        :key="item.id"
        @click.stop="handleFabAction(item)"
      >
        <view class="fab-item-icon">
          <uni-icons :type="item.icon" size="20" color="#4c6ef5" />
        </view>
        <text class="fab-item-text">{{ item.label }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import store from '@/stores/index.js'

// 响应式数据
const statusBarHeight = ref(0)
const unreadCount = ref(3)
const fabMenuVisible = ref(false)

// 公司信息
const companyInfo = reactive({
  name: '燃气管理系统',
  slogan: '安全·高效·智能',
  logo: '/static/logo.png'
})

// 统计数据
const statsData = reactive([
  {
    id: 1,
    label: '新任务',
    value: 8,
    icon: 'list',
    color: '#1890ff',
    trend: 'up',
    change: '+12%'
  },
  {
    id: 2,
    label: '进行中',
    value: 5,
    icon: 'gear',
    color: '#faad14',
    trend: 'stable',
    change: '0%'
  },
  {
    id: 3,
    label: '已完成',
    value: 23,
    icon: 'checkmarkempty',
    color: '#52c41a',
    trend: 'up',
    change: '+8%'
  },
  {
    id: 4,
    label: '紧急',
    value: 2,
    icon: 'fire',
    color: '#ff4d4f',
    trend: 'down',
    change: '-1'
  }
])

// 快速操作
const quickActions = reactive([
  { id: 1, label: '扫码', icon: 'scan', color: '#1890ff' },
  // { id: 2, label: '上报', icon: 'compose', color: '#52c41a' },
  { id: 3, label: '维修', icon: 'gear', color: '#ff4d4f' },
  { id: 4, label: '统计', icon: 'bars', color: '#13c2c2' }
])

// 今日任务
const todayTasks = reactive([
  {
    id: 1,
    title: '燃气管道检修',
    address: '建设路123号',
    time: '09:00',
    status: 'pending',
    priority: 'high'
  },
  {
    id: 2,
    title: '安全阀更换',
    address: '解放路456号',
    time: '14:30',
    status: 'in-progress',
    priority: 'medium'
  },
  {
    id: 3,
    title: '燃气表读数',
    address: '人民路789号',
    time: '16:00',
    status: 'completed',
    priority: 'low'
  }
])



// 悬浮菜单项
const fabMenuItems = reactive([
  // { id: 1, label: '快速上报', icon: 'compose' },
  { id: 2, label: '扫码', icon: 'scan' },
  // { id: 3, label: '导航', icon: 'navigate' }
])

// 计算属性
const userInfo = computed(() => store.state.user)

const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

// 方法
const initPage = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
}

const getRoleText = (role) => {
  const roleMap = {
    'maintenance': '维保员',
    'inspector': '巡检员',
    'delivery': '配送员'
  }
  return roleMap[role] || '工作人员'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    'high': '紧急',
    'medium': '普通',
    'low': '一般'
  }
  return priorityMap[priority] || '普通'
}

const getTrendIcon = (trend) => {
  const iconMap = {
    'up': 'up',
    'down': 'down',
    'stable': 'minus'
  }
  return iconMap[trend] || 'minus'
}

const getTrendColor = (trend) => {
  const colorMap = {
    'up': '#52c41a',
    'down': '#ff4d4f',
    'stable': '#999'
  }
  return colorMap[trend] || '#999'
}

// 事件处理
const handleNotification = () => {
  uni.navigateTo({
    url: '/pages/message/message-list'
  })
}

const handleStatClick = (stat) => {
  console.log('点击统计卡片:', stat)
  // 根据不同类型跳转到对应页面
}

const handleQuickAction = (action) => {
  console.log('快速操作:', action)
  switch (action.id) {
    case 1: // 扫码
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果:', res)
        }
      })
      break
    case 2: // 上报
      uni.navigateTo({
        url: '/pages/report/report'
      })
      break
    default:
      uni.showToast({
        title: `${action.label}功能开发中`,
        icon: 'none'
      })
  }
}

const goToTaskList = () => {
  uni.switchTab({
    url: '/pages/task/task-list'
  })
}

const goToTaskDetail = (task) => {
  uni.navigateTo({
    url: `/pages/task/task-detail?id=${task.id}`
  })
}

const toggleFabMenu = () => {
  fabMenuVisible.value = !fabMenuVisible.value
}

const hideFabMenu = () => {
  fabMenuVisible.value = false
}

const handleFabAction = (item) => {
  console.log('悬浮菜单操作:', item)
  hideFabMenu()

  switch (item.id) {
    case 1: // 快速上报
      uni.navigateTo({
        url: '/pages/report/report'
      })
      break
    case 2: // 扫码
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果:', res)
        }
      })
      break
    case 3: // 导航
      uni.showToast({
        title: '导航功能开发中',
        icon: 'none'
      })
      break
  }
}

// 生命周期
onMounted(() => {
  initPage()
})
</script>

<style lang="scss" scoped>
/* Vue3 Setup 现代化Dashboard设计 */
.dashboard-page {
  min-height: 100vh;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-color: #f8fafc;
  position: relative;
}

/* 状态栏占位 */
.status-bar {
  background-color: $uni-color-primary;
}

/* Header区域样式 */
.header-section {
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-color: $uni-color-primary;
  padding: 32rpx;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
  }
}

/* 公司品牌区域 */
.brand-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.brand-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.logo-image {
  width: 50rpx;
  height: 50rpx;
}

.brand-info {
  flex: 1;
  margin-left: 24rpx;
}

.brand-name {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
  line-height: 1.2;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  margin-right: 16rpx;
}

.brand-slogan {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8rpx;
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
    background: rgba(255, 255, 255, 0.3);
  }
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
  font-weight: 600;
  border: 3rpx solid white;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
}

/* 用户欢迎区域 */
.welcome-area {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.user-avatar {
  position: relative;
  margin-right: 24rpx;
}

.avatar-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

.online-status {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  background: #2ed573;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.welcome-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.welcome-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  line-height: 1.3;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.role-badge {
  margin-left: 20rpx;
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 主内容滚动区域 */
.main-scroll {
  flex: 1;
  background: #f8fafc;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -16rpx;
  position: relative;
  z-index: 2;
}

.content-container {
  padding: 48rpx 32rpx 32rpx;
}

/* 通用section样式 */
.section-title {
  margin-bottom: 32rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
}

.title-desc {
  font-size: 26rpx;
  color: #64748b;
  margin-top: 8rpx;
  font-weight: 400;
  margin-left: 20rpx;
}

/* 数据统计卡片组 */
.stats-section {
  margin-bottom: 48rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-card {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
    border-color: rgba(102, 126, 234, 0.3);
  }

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0.8;
  }
}

/* 卡片头部布局 */
.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.stat-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    border-radius: 16rpx;
  }
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;

  &.up {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
  }

  &.down {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
  }

  &.stable {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
  }
}

.trend-value {
  font-size: 20rpx;
  font-weight: 700;
  line-height: 1;
}

/* 卡片主体布局 */
.stat-body {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 44rpx;
  font-weight: 800;
  color: #0f172a;
  line-height: 1;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.stat-label {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.2;
}

/* 快速操作区域 */
.quick-actions-section {
  margin-bottom: 48rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.action-item {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.action-label {
  font-size: 26rpx;
  color: #374151;
  font-weight: 500;
  text-align: center;
}

/* 今日任务区域 */
.tasks-section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.header-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20rpx;
  transition: all 0.3s ease;

  &:active {
    background: rgba(102, 126, 234, 0.2);
  }
}

.action-text {
  font-size: 26rpx;
  color: #4c6ef5;
  font-weight: 500;
}

.task-list {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
}

.task-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8fafc;
  }
}

.task-status {
  margin-right: 24rpx;

  &.status-pending .status-dot {
    background: #faad14;
  }

  &.status-in-progress .status-dot {
    background: #4c6ef5;
  }

  &.status-completed .status-dot {
    background: #52c41a;
  }
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.task-content {
  flex: 1;
}

.task-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
  margin-bottom: 12rpx;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.task-location,
.task-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-text,
.time-text {
  font-size: 24rpx;
  color: #64748b;
}

.task-priority {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;

  &.priority-high {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
  }

  &.priority-medium {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
  }

  &.priority-low {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
  }
}

.priority-text {
  font-size: 22rpx;
}

.empty-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  gap: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #94a3b8;
  font-weight: 500;
}



/* 悬浮操作按钮 */
.floating-action {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
  z-index: 100;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.6);
  }

  &::before {
    content: '';
    position: absolute;
    top: 8rpx;
    left: 8rpx;
    right: 8rpx;
    bottom: 8rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    backdrop-filter: blur(10rpx);
    z-index: -1;
  }
}

.fab-icon {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 悬浮菜单 */
.fab-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 32rpx;
  padding-bottom: 280rpx;
}

.fab-menu-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: white;
  padding: 24rpx 32rpx;
  border-radius: 28rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    background: #f8fafc;
    transform: scale(0.95);
  }
}

.fab-item-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-item-text {
  font-size: 30rpx;
  color: #1e293b;
  font-weight: 600;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 120rpx;
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100rpx);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>