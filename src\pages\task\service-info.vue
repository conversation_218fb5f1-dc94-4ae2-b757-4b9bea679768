<template>
  <view class="service-info-container">
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
      <!-- 任务信息卡片 -->
      <view class="section-card task-info-card">
        <view class="card-body">
          <view class="task-info-header">
            <view class="task-number">{{ taskDetail.taskNumber }}</view>
            <view class="task-title">{{ taskDetail.title }}</view>
          </view>
          <view class="info-note">
            <uni-icons type="info" size="16" color="#4c6ef5"></uni-icons>
            <text class="note-text">请完善以下服务信息，提交后将进入后台审核流程</text>
          </view>
        </view>
      </view>
      <!-- 服务商选择 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper service-icon">
              <uni-icons type="shop-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">服务商选择</text>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <view class="service-provider-list">
            <view 
              class="provider-item"
              :class="{ active: serviceInfo.serviceProvider === provider.id }"
              v-for="provider in serviceProviders"
              :key="provider.id"
              @click="selectServiceProvider(provider.id)"
            >
              <view class="provider-info">
                <text class="provider-name">{{ provider.name }}</text>
                <text class="provider-desc">{{ provider.description }}</text>
              </view>
              <view class="provider-check">
                <uni-icons 
                  v-if="serviceInfo.serviceProvider === provider.id"
                  type="checkmarkempty" 
                  size="20" 
                  color="#4c6ef5"
                ></uni-icons>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 紧急联系人 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper contact-icon">
              <uni-icons type="contact-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">紧急联系人</text>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <view class="form-item">
            <text class="form-label">联系人姓名</text>
            <input 
              class="form-input"
              placeholder="请输入紧急联系人姓名"
              v-model="serviceInfo.emergencyContact.name"
            />
          </view>
          <view class="form-item">
            <text class="form-label">联系电话</text>
            <input 
              class="form-input"
              placeholder="请输入联系电话"
              type="number"
              v-model="serviceInfo.emergencyContact.phone"
            />
          </view>
          <view class="form-item">
            <text class="form-label">与用户关系</text>
            <picker 
              :range="relationshipOptions"
              :value="relationshipIndex"
              @change="onRelationshipChange"
            >
              <view class="picker-input">
                <text :class="{ placeholder: !serviceInfo.emergencyContact.relationship }">
                  {{ serviceInfo.emergencyContact.relationship || '请选择关系' }}
                </text>
                <uni-icons type="down" size="14" color="#999"></uni-icons>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 每日关阀时间设置 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper time-icon">
              <uni-icons type="time-filled" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">每日关阀时间</text>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <view class="time-setting">
            <view class="time-item">
              <text class="time-label">开阀时间</text>
              <picker 
                mode="time"
                :value="serviceInfo.valveSchedule.openTime"
                @change="onOpenTimeChange"
              >
                <view class="time-picker">
                  <text>{{ serviceInfo.valveSchedule.openTime || '请选择时间' }}</text>
                  <uni-icons type="time" size="16" color="#4c6ef5"></uni-icons>
                </view>
              </picker>
            </view>
            <view class="time-item">
              <text class="time-label">关阀时间</text>
              <picker 
                mode="time"
                :value="serviceInfo.valveSchedule.closeTime"
                @change="onCloseTimeChange"
              >
                <view class="time-picker">
                  <text>{{ serviceInfo.valveSchedule.closeTime || '请选择时间' }}</text>
                  <uni-icons type="time" size="16" color="#4c6ef5"></uni-icons>
                </view>
              </picker>
            </view>
          </view>
          <view class="time-note">
            <uni-icons type="info" size="14" color="#faad14"></uni-icons>
            <text class="note-text">建议设置合理的开关阀时间，确保用气安全</text>
          </view>
        </view>
      </view>

      <!-- 店铺/企业用户额外字段 -->
      <view v-if="isBusinessUser" class="section-card">
        <view class="card-header">
          <view class="header-left">
            <view class="icon-wrapper business-icon">
              <uni-icons type="shop" size="18" color="#fff"></uni-icons>
            </view>
            <text class="section-title">企业信息</text>
          </view>
        </view>

        <view class="card-body">
          <!-- 营业执照 -->
          <view class="form-item">
            <text class="form-label">营业执照号码 <text class="required-mark">*</text></text>
            <input 
              class="form-input"
              placeholder="请输入营业执照号码"
              v-model="serviceInfo.businessInfo.licenseNumber"
            />
          </view>

          <!-- 营业执照有效期 -->
          <view class="form-item">
            <text class="form-label">营业执照有效期 <text class="required-mark">*</text></text>
            <picker 
              mode="date"
              :value="serviceInfo.businessInfo.licenseExpiry"
              @change="onLicenseExpiryChange"
            >
              <view class="picker-input">
                <text :class="{ placeholder: !serviceInfo.businessInfo.licenseExpiry }">
                  {{ serviceInfo.businessInfo.licenseExpiry || '请选择有效期' }}
                </text>
                <uni-icons type="calendar" size="14" color="#999"></uni-icons>
              </view>
            </picker>
          </view>

          <!-- 店铺图片 -->
          <view class="form-item">
            <text class="form-label">店铺图片 <text class="required-mark">*</text></text>
            <view class="upload-grid">
              <view 
                class="upload-slot" 
                v-for="(image, index) in serviceInfo.businessInfo.shopPhotos" 
                :key="index"
                @click="previewImage(serviceInfo.businessInfo.shopPhotos, index)"
              >
                <image class="upload-image" :src="image" mode="aspectFill" />
                <view class="delete-btn" @click.stop="deleteShopImage(index)">
                  <uni-icons type="closeempty" size="14" color="#fff"></uni-icons>
                </view>
              </view>
              <view
                v-if="serviceInfo.businessInfo.shopPhotos.length < 6"
                class="upload-slot add-slot"
                @click="chooseShopImage"
              >
                <uni-icons type="plus" size="24" color="#ccc"></uni-icons>
                <text class="add-text">添加照片</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <button class="secondary-button" @click="saveDraft">
          <text class="button-text">保存草稿</text>
        </button>
        <button
          class="main-button"
          :class="{ disabled: !canSubmit }"
          @click="submitServiceInfo"
          :disabled="!canSubmit"
        >
          <text class="button-text">提交信息</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { taskApi } from '@/api/index.js'
import { showToast, showConfirm } from '@/utils/index.js'

// 响应式数据
const taskId = ref('')
const taskDetail = ref({})
const isBusinessUser = ref(true) // 是否为企业用户，实际应该从任务详情中获取

// 服务信息数据
const serviceInfo = ref({
  serviceProvider: '',
  emergencyContact: {
    name: '',
    phone: '',
    relationship: ''
  },
  valveSchedule: {
    openTime: '',
    closeTime: ''
  },
  businessInfo: {
    licenseNumber: '',
    licenseExpiry: '',
    shopPhotos: []
  }
})

// 选项数据
const serviceProviders = ref([
  { id: '1', name: '燃气公司A', description: '专业燃气设备安装维护' },
  { id: '2', name: '燃气公司B', description: '24小时应急服务' },
  { id: '3', name: '燃气公司C', description: '一站式燃气解决方案' }
])

const relationshipOptions = ['本人', '配偶', '子女', '父母', '兄弟姐妹', '朋友', '同事', '其他']
const relationshipIndex = ref(0)

// 计算属性
const canSubmit = computed(() => {
  const basic = serviceInfo.value.serviceProvider && 
                serviceInfo.value.emergencyContact.name &&
                serviceInfo.value.emergencyContact.phone &&
                serviceInfo.value.emergencyContact.relationship &&
                serviceInfo.value.valveSchedule.openTime &&
                serviceInfo.value.valveSchedule.closeTime

  if (!isBusinessUser.value) return basic

  return basic && 
         serviceInfo.value.businessInfo.licenseNumber &&
         serviceInfo.value.businessInfo.licenseExpiry &&
         serviceInfo.value.businessInfo.shopPhotos.length > 0
})

// 页面生命周期
onLoad((options) => {
  taskId.value = options.taskId
  loadTaskDetail()
})

// 方法
const loadTaskDetail = async () => {
  // 模拟加载任务详情
  taskDetail.value = {
    taskNumber: `GAS${String(taskId.value).padStart(6, '0')}`,
    title: '燃气设备安装任务'
  }
}

const selectServiceProvider = (providerId) => {
  serviceInfo.value.serviceProvider = providerId
}

const onRelationshipChange = (e) => {
  relationshipIndex.value = e.detail.value
  serviceInfo.value.emergencyContact.relationship = relationshipOptions[e.detail.value]
}

const onOpenTimeChange = (e) => {
  serviceInfo.value.valveSchedule.openTime = e.detail.value
}

const onCloseTimeChange = (e) => {
  serviceInfo.value.valveSchedule.closeTime = e.detail.value
}

const onLicenseExpiryChange = (e) => {
  serviceInfo.value.businessInfo.licenseExpiry = e.detail.value
}

const chooseShopImage = () => {
  uni.chooseImage({
    count: 6 - serviceInfo.value.businessInfo.shopPhotos.length,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      serviceInfo.value.businessInfo.shopPhotos.push(...res.tempFilePaths)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      showToast('选择图片失败')
    }
  })
}

const deleteShopImage = (index) => {
  serviceInfo.value.businessInfo.shopPhotos.splice(index, 1)
}

const previewImage = (images, current) => {
  uni.previewImage({
    urls: images,
    current: current
  })
}

const saveDraft = async () => {
  try {
    // 这里应该调用API保存草稿
    showToast('草稿已保存')
  } catch (error) {
    showToast('保存失败')
  }
}

const submitServiceInfo = async () => {
  if (!canSubmit.value) {
    showToast('请完成所有必填信息')
    return
  }

  const confirmed = await showConfirm('确认提交服务信息？提交后将进入后台审核流程')
  if (!confirmed) return

  try {
    // 调用API提交服务信息，更新任务状态为 under_review
    await taskApi.submitServiceInfo(taskId.value, serviceInfo.value)
    showToast('信息提交成功，已进入后台审核流程')

    // 跳转回任务详情页面
    uni.redirectTo({
      url: `/pages/task/task-detail?id=${taskId.value}`
    })
  } catch (error) {
    showToast('提交失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
.service-info-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

// 任务信息卡片
.task-info-card {
  margin-bottom: 32rpx;
}

.task-info-header {
  margin-bottom: 24rpx;
}

.task-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
}

.task-title {
  font-size: 28rpx;
  color: #8c8c8c;
  line-height: 1.4;
}

.info-note {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f7ff 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #4c6ef5;
}

.note-text {
  font-size: 26rpx;
  color: #262626;
  line-height: 1.4;
}

// 内容滚动区域
.content-scroll {
  flex: 1;
  padding: 32rpx 32rpx 120rpx;
  width: 100%;
  box-sizing: border-box;
}

// 卡片样式
.section-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.service-icon { background: linear-gradient(135deg, #52c41a, #73d13d); }
  &.contact-icon { background: linear-gradient(135deg, #4c6ef5, #6c5ce7); }
  &.time-icon { background: linear-gradient(135deg, #faad14, #ffc53d); }
  &.business-icon { background: linear-gradient(135deg, #722ed1, #9254de); }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.required-mark {
  color: #ff6b6b;
  font-size: 28rpx;
}

.card-body {
  padding: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

// 服务商选择
.service-provider-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.provider-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;

  &.active {
    border-color: #4c6ef5;
    background: #f0f4ff;
  }

  &:active {
    transform: scale(0.98);
  }
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.provider-desc {
  font-size: 24rpx;
  color: #8c8c8c;
}

.provider-check {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表单项
.form-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  font-size: 28rpx;
  color: #262626;
  transition: all 0.2s ease;

  &:focus {
    border-color: #4c6ef5;
    background: #fff;
  }
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  cursor: pointer;

  &:active {
    border-color: #4c6ef5;
    background: #fff;
  }

  .placeholder {
    color: #999;
  }
}

// 时间设置
.time-setting {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.time-label {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  min-width: 120rpx;
}

.time-picker {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #262626;
  cursor: pointer;

  &:active {
    background: #e9ecef;
  }
}

.time-note {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: #fffbe6;
  border-radius: 12rpx;
  border-left: 4rpx solid #faad14;
}

.note-text {
  font-size: 24rpx;
  color: #8c6e00;
}

// 上传网格
.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.upload-slot {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
}

.upload-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-slot {
  border: 2rpx dashed #ddd;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.2s ease;

  &:active {
    border-color: #4c6ef5;
    background: #f0f4ff;
  }
}

.add-text {
  font-size: 22rpx;
  color: #999;
}

.safe-bottom {
  height: 32rpx;
}

// 底部操作栏
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.main-button, .secondary-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.main-button {
  flex: 2;
  background: linear-gradient(135deg, #4c6ef5, #6c5ce7);
  color: #fff;

  &.disabled {
    background: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
  }
}

.secondary-button {
  flex: 1;
  background: #f8f9fa;
  border: 1rpx solid #e8e8e8;
  color: #666;

  &:active {
    background: #e9ecef;
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
}
</style>
