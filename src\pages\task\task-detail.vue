<template>
  <view class="page-container">
    <!-- 顶部状态栏 -->
    <view class="header-section">
      <view class="task-header">
        <view class="task-info">
          <text class="task-number">{{ taskDetail.taskNumber }}</text>
          <text class="task-title">{{ taskDetail.title }}</text>
        </view>
        <view class="task-badges">
          <view class="status-badge" :class="'status-' + taskDetail.status">
            <uni-icons type="flag" size="12" color="#fff"></uni-icons>
            <text class="badge-text">{{ getStatusText(taskDetail.status) }}</text>
          </view>
          <view class="type-badge" :class="'type-' + taskDetail.type">
            <uni-icons type="settings" size="12" color="#fff"></uni-icons>
            <text class="badge-text">{{ getTaskTypeText(taskDetail.type) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view class="content-scroll" scroll-y="true">
      <!-- 客户信息卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">客户信息</text>
          </view>
          <view class="header-actions">
            <view class="action-btn phone-btn" @click="callCustomer">
              <uni-icons type="phone" size="16" color="#fff"></uni-icons>
            </view>
            <view class="action-btn nav-btn" @click="openNavigation">
              <uni-icons type="location" size="16" color="#fff"></uni-icons>
            </view>
          </view>
        </view>

        <view class="card-body">
          <view class="customer-main">
            <view class="customer-name">
              <uni-icons type="person" size="16" color="#4c6ef5"></uni-icons>
              <text class="name-text">{{ taskDetail.customerName }}</text>
            </view>
            <view class="customer-phone" @click="callCustomer">
              <uni-icons type="phone" size="16" color="#52c41a"></uni-icons>
              <text class="phone-text">{{ taskDetail.customerPhone }}</text>
            </view>
          </view>

          <view class="address-section">
            <view class="address-item" @click="openNavigation">
              <view class="address-icon">
                <uni-icons type="location" size="16" color="#ff6b6b"></uni-icons>
              </view>
              <view class="address-content">
                <text class="address-main">{{ taskDetail.address }}</text>
                <text v-if="taskDetail.detailAddress" class="address-detail">{{ taskDetail.detailAddress }}</text>
              </view>
              <uni-icons type="right" size="14" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 客户设备卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">客户设备</text>
          </view>
        </view>

        <view class="card-body">
          <view class="equipment-summary">
            <!-- 气瓶设备统计 -->
            <view class="equipment-item" @click="showGasCylinders">
              <view class="equipment-icon-wrapper gas-cylinder">
                <uni-icons type="fire" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">气瓶设备</text>
                <text class="equipment-count">{{ gasCylinders.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalGasCylinders }}台正常</text>
                <text class="abnormal-count" v-if="abnormalGasCylinders > 0">{{ abnormalGasCylinders }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>

            <!-- 报警器设备统计 -->
            <view class="equipment-item" @click="showAlarms">
              <view class="equipment-icon-wrapper alarm">
                <uni-icons type="sound" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">报警器设备</text>
                <text class="equipment-count">{{ alarms.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalAlarms }}台正常</text>
                <text class="abnormal-count" v-if="abnormalAlarms > 0">{{ abnormalAlarms }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务信息卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">服务内容</text>
          </view>
        </view>

        <view class="card-body">
          <view class="service-info">
            <view class="service-row">
              <view class="service-item">
                <view class="service-icon-wrapper">
                  <uni-icons type="flag" size="14" color="#faad14"></uni-icons>
                </view>
                <view class="service-content">
                  <text class="service-label">任务类型</text>
                  <text class="service-value">{{ getTaskTypeText(taskDetail.type) }}</text>
                </view>
              </view>

              <view class="service-item">
                <view class="service-icon-wrapper">
                  <uni-icons type="calendar" size="14" color="#1890ff"></uni-icons>
                </view>
                <view class="service-content">
                  <text class="service-label">预约时间</text>
                  <text class="service-value">{{ formatDateTime(taskDetail.appointmentTime) }}</text>
                </view>
              </view>
            </view>

            <view class="requirements-section">
              <view class="requirements-header">
                <uni-icons type="compose" size="14" color="#52c41a"></uni-icons>
                <text class="requirements-title">服务要求</text>
              </view>
              <text class="requirements-content">{{ taskDetail.requirements }}</text>
            </view>

            <view v-if="taskDetail.remarks" class="remarks-section">
              <view class="remarks-header">
                <uni-icons type="chatbubble" size="14" color="#ff6b6b"></uni-icons>
                <text class="remarks-title">备注信息</text>
              </view>
              <text class="remarks-content">{{ taskDetail.remarks }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 使用耗材卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">使用耗材</text>
          </view>
          <view class="action-btn scan-btn" @click="scanParts">
            <text class="scan-text">添加</text>
          </view>
        </view>

        <view class="card-body">
          <view v-if="taskDetail.parts && taskDetail.parts.length > 0" class="parts-container">
            <view
              class="part-card"
              v-for="part in taskDetail.parts"
              :key="part.id"
            >
              <view class="part-header">
                <view class="part-icon">
                  <uni-icons type="cube" size="16" color="#722ed1"></uni-icons>
                </view>
                <view class="part-main">
                  <text class="part-name">{{ part.name }}</text>
                  <text class="part-spec">{{ part.specification }}</text>
                </view>
                <view class="part-quantity">
                  <text class="quantity-number">{{ part.quantity }}</text>
                  <text class="quantity-unit">{{ part.unit }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-state">
            <view class="empty-icon">
              <uni-icons type="inbox" size="32" color="#d9d9d9"></uni-icons>
            </view>
            <text class="empty-text">暂无使用耗材要求</text>
          </view>
        </view>
      </view>
      
      <!-- 操作流程卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">操作流程</text>
          </view>
        </view>

        <view class="card-body">
          <view class="process-timeline">
            <view
              class="timeline-item"
              :class="{
                'is-active': step.status === 'active',
                'is-completed': step.status === 'completed',
                'is-failed': step.status === 'failed',
                'is-last': index === processSteps.length - 1,
                'is-clickable': step.title === '前往现场'
              }"
              v-for="(step, index) in processSteps"
              :key="index"
              @click="handleStepClick(step)"
            >
              <view class="timeline-node">
                <view class="node-icon">
                  <uni-icons
                    v-if="step.status === 'completed'"
                    type="checkmarkempty"
                    size="14"
                    color="#fff"
                  ></uni-icons>
                  <uni-icons
                    v-else-if="step.status === 'failed'"
                    type="close"
                    size="14"
                    color="#fff"
                  ></uni-icons>
                  <text v-else class="node-number">{{ index + 1 }}</text>
                </view>
              </view>

              <view class="timeline-content">
                <view class="step-header">
                  <text class="step-title">{{ step.title }}</text>
                  <text v-if="step.time" class="step-time">
                    <uni-icons type="clock" size="12" color="#999"></uni-icons>
                    {{ formatDateTime(step.time) }}
                  </text>
                </view>
                <text class="step-desc">{{ step.description }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 安全环境不合格警告卡片 -->
      <view v-if="taskDetail.status === 'safety_failed'" class="section-card warning-card">
        <view class="card-body">
          <view class="safety-failed-warning">
            <view class="warning-header">
              <uni-icons type="info" size="24" color="#ff6b6b"></uni-icons>
              <text class="warning-title">安全环境不合格</text>
            </view>
            <text class="warning-desc">现场安全环境不符合作业要求，请处理安全隐患后重新检查，或联系主管寻求帮助。</text>
          </view>
        </view>
      </view>

      <!-- 客户设备卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">客户设备</text>
          </view>
        </view>

        <view class="card-body">
          <view class="equipment-summary">
            <!-- 气瓶设备统计 -->
            <view class="equipment-item" @click="showGasCylinders">
              <view class="equipment-icon-wrapper gas-cylinder">
                <uni-icons type="fire" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">气瓶设备</text>
                <text class="equipment-count">{{ gasCylinders.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalGasCylinders }}台正常</text>
                <text class="abnormal-count" v-if="abnormalGasCylinders > 0">{{ abnormalGasCylinders }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>

            <!-- 报警器设备统计 -->
            <view class="equipment-item" @click="showAlarms">
              <view class="equipment-icon-wrapper alarm">
                <uni-icons type="sound" size="20" color="#fff"></uni-icons>
              </view>
              <view class="equipment-info">
                <text class="equipment-name">报警器设备</text>
                <text class="equipment-count">{{ alarms.length }}台</text>
              </view>
              <view class="equipment-status">
                <text class="normal-count">{{ normalAlarms }}台正常</text>
                <text class="abnormal-count" v-if="abnormalAlarms > 0">{{ abnormalAlarms }}台异常</text>
              </view>
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 工作记录卡片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">备注</text>
          </view>
          <view class="action-btn add-btn" @click="addRemark">
            <text class="add-text">添加</text>
          </view>
        </view>

        <view class="card-body">
          <view v-if="workRecords.length > 0" class="records-container">
            <view
              class="record-card"
              v-for="record in workRecords"
              :key="record.id"
            >
              <view class="record-header">
                <view class="record-type-badge">
                  <uni-icons type="flag" size="12" color="#eb2f96"></uni-icons>
                  <text class="record-type">{{ record.type }}</text>
                </view>
                <view class="record-time">
                  <uni-icons type="clock" size="12" color="#999"></uni-icons>
                  <text class="time-text">{{ formatDateTime(record.time) }}</text>
                </view>
              </view>

              <text class="record-content">{{ record.content }}</text>

              <view v-if="record.images && record.images.length > 0" class="record-images">
                <view
                  class="image-item"
                  v-for="(image, index) in record.images"
                  :key="index"
                  @click="previewImageHandler(record.images, index)"
                >
                  <image
                    class="record-image"
                    :src="image"
                    mode="aspectFill"
                  />
                  <view class="image-mask">
                    <uni-icons type="eye" size="14" color="#fff"></uni-icons>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view v-else class="empty-state">
            <view class="empty-icon">
              <uni-icons type="compose" size="32" color="#d9d9d9"></uni-icons>
            </view>
            <text class="empty-text">暂无备注</text>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <button
          v-if="taskDetail.status === 'new'"
          class="main-button accept-btn"
          @click="acceptTask"
        >
          <uni-icons type="checkbox" size="16" color="#fff"></uni-icons>
          <text class="button-text">接单任务</text>
        </button>

        <button
          v-if="taskDetail.status === 'accepted'"
          class="main-button arrive-btn"
          @click="arriveAtSite"
        >
          <uni-icons type="location" size="16" color="#fff"></uni-icons>
          <text class="button-text">到达现场</text>
        </button>

        <template v-if="taskDetail.status === 'arrived'">
          <button
            class="main-button safety-pass-btn"
            @click="confirmSafetyCheck(true)"
          >
            <text class="button-text">合格</text>
          </button>
          <button
            class="secondary-button safety-fail-btn"
            @click="confirmSafetyCheck(false)"
          >
            <text class="button-text">不合格</text>
          </button>
        </template>

        <template v-if="taskDetail.status === 'safety_failed'">
          <button class="main-button recheck-btn" @click="recheckSafety">
            <uni-icons type="refresh" size="16" color="#fff"></uni-icons>
            <text class="button-text">重新检查</text>
          </button>
        </template>

        <button
          v-if="taskDetail.status === 'safety_checked'"
          class="main-button start-btn"
          @click="startTask"
        >
          <uni-icons type="play-right" size="16" color="#fff"></uni-icons>
          <text class="button-text">开始作业</text>
        </button>

        <template v-if="taskDetail.status === 'progress'">
          <!-- <button class="secondary-button" @click="reportProblem">
            <uni-icons type="info" size="16" color="#ff6b6b"></uni-icons>
            <text class="button-text">上报问题</text>
          </button> -->
          <button class="main-button upload-btn" @click="goToUploadMaterials">
            <uni-icons type="compose" size="16" color="#fff"></uni-icons>
            <text class="button-text">填写资料</text>
          </button>
        </template>



        <template v-if="taskDetail.status === 'info_filled' || taskDetail.status === 'under_review'">
          <view class="status-info">
            <uni-icons type="clock" size="16" color="#faad14"></uni-icons>
            <text class="status-text">等待后台审核中...</text>
          </view>
        </template>

        <template v-if="taskDetail.status === 'completed'">
          <button class="secondary-button" @click="viewReport">
            <uni-icons type="eye" size="16" color="#1890ff"></uni-icons>
            <text class="button-text">查看报告</text>
          </button>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { taskApi } from '@/api/index.js'
import { formatDate, makePhoneCall, openMap, scanCode, previewImage, showToast, showConfirm } from '@/utils/index'

// 响应式数据
const taskId = ref('')
const taskDetail = ref({})
const workRecords = ref([])
const processSteps = ref([
  { title: '任务接收', description: '确认接收任务', status: 'pending' },
  { title: '前往现场', description: '导航到客户地址', status: 'pending' },
  { title: '安全环境合格', description: '确认现场安全环境是否合格', status: 'pending' },
  { title: '开始作业', description: '到达现场开始工作', status: 'pending' },
  { title: '填写资料', description: '上传照片并填写服务信息', status: 'pending' },
  { title: '后台审核', description: '等待后台审核资料', status: 'pending' },
  { title: '客户支付', description: '客户支付耗材使用费用', status: 'pending' },
  { title: '完成作业', description: '审核通过，完成作业', status: 'pending' },
  { title: '客户确认', description: '客户签字确认', status: 'pending' }
])

// 气瓶设备数据
const gasCylinders = ref([
  {
    id: 1,
    code: 'LPG77778888',
    alarmCode: '未设置',
    fillingUnit: '充装单位F',
    fillingTime: '2023-08-10',
    fillingWeight: '11kg',
    alarmStatus: '正常',
    valveStatus: 'open', // open/closed
    batteryLevel: 92,
    status: 'normal' // normal/abnormal
  },
  {
    id: 2,
    code: 'LPG99990000',
    alarmCode: '未设置',
    fillingUnit: '充装单位G',
    fillingTime: '2023-08-10',
    fillingWeight: '10kg',
    alarmStatus: '正常',
    valveStatus: 'closed',
    batteryLevel: 67,
    status: 'normal'
  }
])

// 报警器设备数据
const alarms = ref([
  {
    id: 1,
    type: 'home', // home/commercial/co
    code: 'AL001234567',
    batteryLevel: 85,
    status: 'normal',
    installTime: '2023-06-15'
  },
  {
    id: 2,
    type: 'commercial',
    code: 'AL001234568',
    batteryLevel: 45,
    status: 'low_battery',
    installTime: '2023-05-20'
  },
  {
    id: 3,
    type: 'co',
    code: 'AL001234569',
    batteryLevel: 78,
    status: 'normal',
    installTime: '2023-07-01'
  }
])

// 计算属性
const normalGasCylinders = computed(() => {
  return gasCylinders.value.filter(item => item.status === 'normal').length
})

const abnormalGasCylinders = computed(() => {
  return gasCylinders.value.filter(item => item.status !== 'normal').length
})

const normalAlarms = computed(() => {
  return alarms.value.filter(item => item.status === 'normal').length
})

const abnormalAlarms = computed(() => {
  return alarms.value.filter(item => item.status !== 'normal').length
})

// 方法
const loadTaskDetail = async () => {
  try {
    const detail = await taskApi.getTaskDetail(taskId.value)
    taskDetail.value = detail
    workRecords.value = detail.workRecords || []
    updateProcessSteps()
  } catch (error) {
    showToast('加载失败')
    uni.navigateBack()
  }
}

const updateProcessSteps = () => {
  const status = taskDetail.value.status

  // 重置所有步骤
  processSteps.value.forEach(step => {
    step.status = 'pending'
    step.time = null
  })

  // 根据任务状态更新步骤
  if (status === 'accepted' || status === 'arrived' || status === 'safety_checked' || status === 'safety_failed' || status === 'progress' || status === 'under_review' || status === 'pending_payment' || status === 'completed') {
    processSteps.value[0].status = 'completed'
    processSteps.value[0].time = taskDetail.value.acceptTime
  }

  if (status === 'arrived' || status === 'safety_checked' || status === 'safety_failed' || status === 'progress' || status === 'under_review' || status === 'pending_payment' || status === 'completed') {
    processSteps.value[1].status = 'completed'
    processSteps.value[1].time = taskDetail.value.arriveTime
  }

  if (status === 'safety_checked' || status === 'progress' || status === 'under_review' || status === 'pending_payment' || status === 'completed') {
    processSteps.value[2].status = 'completed'
    processSteps.value[2].time = taskDetail.value.safetyCheckTime
  } else if (status === 'safety_failed') {
    processSteps.value[2].status = 'failed'
    processSteps.value[2].time = taskDetail.value.safetyCheckTime
  }

  if (status === 'progress' || status === 'under_review' || status === 'pending_payment' || status === 'completed') {
    processSteps.value[3].status = 'completed'
    processSteps.value[3].time = taskDetail.value.startTime
  }

  if (status === 'under_review' || status === 'pending_payment' || status === 'completed') {
    processSteps.value[4].status = 'completed'
    processSteps.value[4].time = taskDetail.value.materialsUploadTime
  }

  if (status === 'under_review' || status === 'pending_payment' || status === 'completed') {
    processSteps.value[5].status = 'completed'
    processSteps.value[5].time = taskDetail.value.reviewStartTime
  }

  if (status === 'pending_payment' || status === 'completed') {
    processSteps.value[6].status = 'completed'
    processSteps.value[6].time = taskDetail.value.paymentTime
  }

  if (status === 'completed') {
    processSteps.value[7].status = 'completed'
    processSteps.value[8].status = 'completed'
    processSteps.value[7].time = taskDetail.value.completeTime
  }

  // 设置当前活动步骤
  if (status === 'new') {
    processSteps.value[0].status = 'active'
  } else if (status === 'accepted') {
    processSteps.value[1].status = 'active'
  } else if (status === 'arrived') {
    processSteps.value[2].status = 'active' // 到达现场后，下一步是安全环境检查
  } else if (status === 'safety_failed') {
    processSteps.value[2].status = 'failed' // 安全检查失败，显示失败状态
  } else if (status === 'safety_checked') {
    processSteps.value[3].status = 'active' // 安全检查完成后，下一步是开始作业
  } else if (status === 'progress') {
    processSteps.value[4].status = 'active' // 开始作业后，下一步是填写资料
  } else if (status === 'under_review') {
    processSteps.value[5].status = 'active' // 审核中，高亮审核步骤
  } else if (status === 'pending_payment') {
    processSteps.value[6].status = 'active' // 等待客户支付
  }
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  return formatDate(datetime, 'YYYY-MM-DD HH:mm')
}

// 显示气瓶设备列表
const showGasCylinders = () => {
  uni.navigateTo({
    url: `/pages/equipment/gas-cylinders?taskId=${taskId.value}`
  })
}

// 显示报警器设备列表
const showAlarms = () => {
  uni.navigateTo({
    url: `/pages/equipment/alarms?taskId=${taskId.value}`
  })
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    new: '待接单',
    accepted: '已接单',
    arrived: '已到达',
    safety_checked: '环境已检查',
    safety_failed: '环境不合格',
    progress: '进行中',
    materials_uploaded: '资料已上传',
    info_filled: '信息已填写',
    under_review: '审核中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取任务类型文本
const getTaskTypeText = (type) => {
  const typeMap = {
    install: '安装',
    repair: '维修',
    rectification: '整改'
  }
  return typeMap[type] || type
}

// 拨打客户电话
const callCustomer = () => {
  makePhoneCall(taskDetail.value.customerPhone)
}

// 打开导航
const openNavigation = () => {
  openMap(taskDetail.value.latitude, taskDetail.value.longitude, taskDetail.value.address)
}

// 处理流程步骤点击
const handleStepClick = (step) => {
  // 只有"前往现场"步骤可以点击导航
  if (step.title === '前往现场') {
    openNavigation()
  }
}

// 扫描设备
const scanDevice = async () => {
  try {
    const result = await scanCode()
    showToast('扫描结果: ' + result)
    // 处理设备扫描结果
  } catch (error) {
    showToast('扫描失败')
  }
}

// 添加耗材
const scanParts = () => {
  uni.navigateTo({
    url: `/pages/task/add-parts?taskId=${taskId.value}`
  })
}

// 添加备注
const addRemark = () => {
  uni.navigateTo({
    url: `/pages/task/add-remark?taskId=${taskId.value}`
  })
}

// 预览图片
const previewImageHandler = (images, current) => {
  previewImage(images, current)
}

// 接收任务
const acceptTask = async () => {
  const confirmed = await showConfirm('确认接收此任务？')
  if (!confirmed) return

  try {
    await taskApi.acceptTask(taskId.value)
    showToast('任务接单成功')
    loadTaskDetail()
  } catch (error) {
    showToast('接单失败')
  }
}

// 到达现场
const arriveAtSite = async () => {
  const confirmed = await showConfirm('确认已到达现场？')
  if (!confirmed) return

  try {
    await taskApi.arriveAtSite(taskId.value)
    showToast('已确认到达现场，请检查安全环境')
    loadTaskDetail()
  } catch (error) {
    showToast('操作失败')
  }
}

// 安全环境检查确认
const confirmSafetyCheck = async (isPass) => {
  const message = isPass ? '确认现场安全环境合格？' : '确认现场安全环境不合格？\n不合格将无法继续作业'
  const confirmed = await showConfirm(message)
  if (!confirmed) return

  try {
    await taskApi.confirmSafetyCheck(taskId.value, isPass)
    if (isPass) {
      showToast('安全环境检查通过，可以开始作业')
    } else {
      showToast('安全环境不合格，请联系相关人员处理')
    }
    loadTaskDetail()
  } catch (error) {
    showToast('操作失败')
  }
}

// 联系主管
const contactSupervisor = () => {
  // 这里可以调用主管电话或者打开联系页面
  uni.showModal({
    title: '联系主管',
    content: '主管电话：************\n是否拨打电话？',
    confirmText: '拨打',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: '************'
        })
      }
    }
  })
}

// 重新检查安全环境
const recheckSafety = async () => {
  const confirmed = await showConfirm('确认重新检查安全环境？')
  if (!confirmed) return

  try {
    // 将状态重置为已到达，可以重新进行安全检查
    await taskApi.recheckSafety(taskId.value)
    showToast('已重置为待检查状态')
    loadTaskDetail()
  } catch (error) {
    showToast('操作失败')
  }
}

// 开始任务
const startTask = async () => {
  const confirmed = await showConfirm('确认开始作业？')
  if (!confirmed) return

  try {
    await taskApi.startTask(taskId.value)
    showToast('任务已开始，请上传相关资料')
    loadTaskDetail()
  } catch (error) {
    showToast('开始失败')
  }
}

// 上报问题
const reportProblem = () => {
  uni.navigateTo({
    url: `/pages/task/report-problem?taskId=${taskId.value}`
  })
}

// 跳转到上传资料页面
const goToUploadMaterials = () => {
  uni.navigateTo({
    url: `/pages/task/upload-materials?taskId=${taskId.value}`
  })
}

// 跳转到服务信息页面
const goToServiceInfo = () => {
  uni.navigateTo({
    url: `/pages/task/service-info?taskId=${taskId.value}`
  })
}

// 完成任务
const completeTask = () => {
  uni.navigateTo({
    url: `/pages/task/complete-task?taskId=${taskId.value}`
  })
}

// 查看报告
const viewReport = () => {
  uni.navigateTo({
    url: `/pages/task/task-report?taskId=${taskId.value}`
  })
}

// 页面生命周期
onLoad((options) => {
  taskId.value = options.id
  loadTaskDetail()
})

onShow(() => {
  // 页面显示时刷新数据
  if (taskId.value) {
    loadTaskDetail()
  }
})
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

// 顶部区域
.header-section {
  background: linear-gradient(135deg, #4c6ef5 0%, #6c5ce7 100%);
  padding: 32rpx 32rpx 24rpx;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -20rpx;
    left: 0;
    right: 0;
    height: 20rpx;
    background: #f8f9fa;
    border-radius: 20rpx 20rpx 0 0;
  }
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24rpx;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}

.task-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.task-badges {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex-shrink: 0;
}

.status-badge, .type-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);

  &.status-new { background: rgba(255, 107, 107, 0.9); }
  &.status-accepted { background: rgba(82, 196, 26, 0.9); }
  &.status-progress { background: rgba(24, 144, 255, 0.9); }
  &.status-completed { background: rgba(82, 196, 26, 0.9); }

  &.type-install { background: rgba(82, 196, 26, 0.9); }
  &.type-repair { background: rgba(250, 173, 20, 0.9); }
  &.type-rectification { background: rgba(255, 77, 79, 0.9); }
}

.badge-text {
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
}

// 内容滚动区域
.content-scroll {
  flex: 1;
  width: 100%;
  max-width: 100%;
  padding: 24rpx 32rpx 120rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

// 卡片样式
.section-card {
  width: 100%;
  max-width: 100%;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}



.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  height: 48rpx;
  padding: 0 16rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.phone-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }
  &.nav-btn { background: linear-gradient(135deg, #ff6b6b, #ff8a8a); }
  &.scan-btn { background: linear-gradient(135deg, #1890ff, #40a9ff); }
  &.add-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }

  &:active {
    transform: scale(0.9);
  }

  .scan-text, .add-text {
    color: #fff;
    font-size: 24rpx;
    font-weight: 500;
  }
}

.card-body {
  padding: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

// 客户信息样式
.customer-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.customer-name, .customer-phone {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.name-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
}

.phone-text {
  font-size: 28rpx;
  color: #52c41a;
  font-weight: 500;
}

.customer-phone {
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    opacity: 0.7;
  }
}

.address-section {
  margin-top: 8rpx;
}

.address-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}

.address-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.address-content {
  flex: 1;
  min-width: 0;
}

.address-main {
  font-size: 28rpx;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: block;
}

.address-detail {
  font-size: 26rpx;
  color: #8c8c8c;
  line-height: 1.3;
  display: block;
}

// 设备信息样式
.device-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 24rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.device-icon-small {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.device-info {
  flex: 1;
  min-width: 0;
}

.device-label {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-bottom: 4rpx;
  display: block;
}

.device-value {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
  display: block;
}

.tech-specs {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.specs-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.specs-title {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.specs-content {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.5;
}

// 服务内容样式
.service-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
  max-width: 100%;
}

.service-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.service-icon-wrapper {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.service-content {
  flex: 1;
  min-width: 0;
}

.service-label {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-bottom: 4rpx;
  display: block;
}

.service-value {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
  display: block;
}

.requirements-section, .remarks-section {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.requirements-header, .remarks-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.requirements-title, .remarks-title {
  font-size: 26rpx;
  color: #262626;
  font-weight: 500;
}

.requirements-content, .remarks-content {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.6;
}

// 使用耗材样式
.parts-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.part-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
}

.part-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
}

.part-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.part-main {
  flex: 1;
  min-width: 0;
}

.part-name {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.part-spec {
  font-size: 24rpx;
  color: #8c8c8c;
  display: block;
}

.part-quantity {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  flex-shrink: 0;
}

.quantity-number {
  font-size: 32rpx;
  color: #722ed1;
  font-weight: 600;
}

.quantity-unit {
  font-size: 24rpx;
  color: #8c8c8c;
}

// 操作流程样式
.process-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  gap: 24rpx;
  padding-bottom: 32rpx;
  position: relative;

  &:not(.is-last)::after {
    content: '';
    position: absolute;
    left: 23rpx;
    top: 48rpx;
    bottom: 0;
    width: 2rpx;
    background: #e8e8e8;
  }

  &.is-completed::after {
    background: #52c41a;
  }

  &.is-active::after {
    background: linear-gradient(to bottom, #4c6ef5 0%, #e8e8e8 50%);
  }

  &.is-failed::after {
    background: #ff6b6b;
  }

  &.is-clickable {
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
    }



    .step-desc::after {
      content: ' · 点击导航';
      font-size: 22rpx;
      color: #4c6ef5;
      font-weight: 500;
    }
  }
}

.timeline-node {
  flex-shrink: 0;
  margin-top: 4rpx;
}

.node-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e8e8e8;

  .is-completed & {
    background: #52c41a;
  }

  .is-active & {
    background: #4c6ef5;
  }

  .is-failed & {
    background: #ff6b6b;
  }
}

.node-number {
  font-size: 24rpx;
  font-weight: 600;
  color: #8c8c8c;

  .is-completed &, .is-active &, .is-failed & {
    color: #fff;
  }
}

.timeline-content {
  flex: 1;
  min-width: 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
  gap: 16rpx;
}

.step-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #262626;

  .is-completed & {
    color: #52c41a;
  }

  .is-active & {
    color: #4c6ef5;
  }

  .is-failed & {
    color: #ff6b6b;
  }
}

.step-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #8c8c8c;
  flex-shrink: 0;
}

.step-desc {
  font-size: 26rpx;
  color: #8c8c8c;
  line-height: 1.4;
}

// 工作记录样式
.records-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.record-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-type-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(235, 47, 150, 0.1);
  border-radius: 20rpx;
}

.record-type {
  font-size: 24rpx;
  font-weight: 500;
  color: #eb2f96;
}

.record-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.time-text {
  font-size: 24rpx;
  color: #8c8c8c;
}

.record-content {
  font-size: 28rpx;
  color: #262626;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.record-images {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
}

.record-image {
  width: 100%;
  height: 100%;
}

.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:active .image-mask {
  opacity: 1;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 32rpx;
  gap: 16rpx;
}

.empty-icon {
  opacity: 0.6;
}

.empty-text {
  font-size: 26rpx;
  color: #8c8c8c;
}

.safe-bottom {
  height: 32rpx;
}

// 底部操作栏
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.main-button, .secondary-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  height: 88rpx;
  border-radius: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.main-button {
  flex: 1;
  color: #fff;

  &.accept-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }
  &.arrive-btn { background: linear-gradient(135deg, #1890ff, #40a9ff); }
  &.safety-pass-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }
  &.recheck-btn { background: linear-gradient(135deg, #faad14, #ffc53d); }
  &.start-btn { background: linear-gradient(135deg, #1890ff, #40a9ff); }
  &.upload-btn { background: linear-gradient(135deg, #722ed1, #9254de); }
  &.info-btn { background: linear-gradient(135deg, #faad14, #ffc53d); }
  &.complete-btn { background: linear-gradient(135deg, #52c41a, #73d13d); }
}

.status-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  background: #fffbe6;
  border-radius: 24rpx;
}

.status-text {
  font-size: 28rpx;
  color: #8c6e00;
  font-weight: 500;
}

.secondary-button {
  flex: 1;
  background: #f8f9fa;

  &.safety-fail-btn {
    background: #fff2f0;
    border: 2rpx solid #ffccc7;

    .button-text {
      color: #ff6b6b;
    }

    &:active {
      background: #ffebe8;
    }
  }

  &:active {
    background: #e9ecef;
  }
  &::after {
    border: none;
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
}
.button-text::after {
  border: none;
}
// 设备统计样式
.equipment-summary {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.equipment-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}

.equipment-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.gas-cylinder {
    background: linear-gradient(135deg, #ff6b6b, #ff8a8a);
  }

  &.alarm {
    background: linear-gradient(135deg, #faad14, #ffc53d);
  }
}

.equipment-info {
  flex: 1;
  min-width: 0;
}

.equipment-name {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.equipment-count {
  font-size: 24rpx;
  color: #8c8c8c;
  display: block;
}

.equipment-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
  flex-shrink: 0;
}

.normal-count {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

.abnormal-count {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: 500;
}

// 警告卡片样式
.warning-card {
  border: 2rpx solid #ffccc7;
  background: #fff2f0;
}

.safety-failed-warning {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.warning-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff6b6b;
}

.warning-desc {
  font-size: 28rpx;
  color: #8c4a4a;
  line-height: 1.6;
}
</style>
