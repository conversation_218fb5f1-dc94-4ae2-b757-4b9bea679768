<template>
  <view class="about-container">
    <!-- 应用信息 -->
    <view class="app-info">
      <view class="app-logo">
        <image src="/static/logo.png" mode="aspectFit" class="logo-image"></image>
      </view>
      <text class="app-name">燃气管理系统</text>
      <text class="app-version">版本 {{ appVersion }}</text>
      <text class="app-desc">专业的燃气设备维护管理平台</text>
    </view>

    <!-- 公司信息 -->
    <view class="company-section">
      <view class="section-header">
        <uni-icons type="home" size="18" color="#4c6ef5"></uni-icons>
        <text class="section-title">公司信息</text>
      </view>
      <view class="info-card">
        <view class="info-item">
          <text class="info-label">公司名称</text>
          <text class="info-value">北京燃气科技有限公司</text>
        </view>
        <view class="info-item">
          <text class="info-label">成立时间</text>
          <text class="info-value">2015年3月</text>
        </view>
        <view class="info-item">
          <text class="info-label">注册资本</text>
          <text class="info-value">5000万元</text>
        </view>
        <view class="info-item">
          <text class="info-label">员工规模</text>
          <text class="info-value">500+人</text>
        </view>
      </view>
    </view>

    <!-- 业务范围 -->
    <view class="business-section">
      <view class="section-header">
        <uni-icons type="gear" size="18" color="#4c6ef5"></uni-icons>
        <text class="section-title">业务范围</text>
      </view>
      <view class="business-grid">
        <view class="business-item" v-for="business in businessList" :key="business.id">
          <view class="business-icon">
            <uni-icons :type="business.icon" size="24" color="#4c6ef5"></uni-icons>
          </view>
          <text class="business-name">{{ business.name }}</text>
          <text class="business-desc">{{ business.desc }}</text>
        </view>
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="contact-section">
      <view class="section-header">
        <uni-icons type="phone" size="18" color="#4c6ef5"></uni-icons>
        <text class="section-title">联系我们</text>
      </view>
      <view class="contact-card">
        <view class="contact-item" @click="callPhone">
          <view class="contact-icon">
            <uni-icons type="phone" size="20" color="#52c41a"></uni-icons>
          </view>
          <view class="contact-info">
            <text class="contact-label">客服热线</text>
            <text class="contact-value">************</text>
          </view>
          <uni-icons type="right" size="14" color="#ccc"></uni-icons>
        </view>
        <view class="contact-item">
          <view class="contact-icon">
            <uni-icons type="email" size="20" color="#1890ff"></uni-icons>
          </view>
          <view class="contact-info">
            <text class="contact-label">邮箱地址</text>
            <text class="contact-value"><EMAIL></text>
          </view>
        </view>
        <view class="contact-item">
          <view class="contact-icon">
            <uni-icons type="location" size="20" color="#fa8c16"></uni-icons>
          </view>
          <view class="contact-info">
            <text class="contact-label">公司地址</text>
            <text class="contact-value">北京市朝阳区建国路88号现代城A座</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright-section">
      <text class="copyright-text">© 2024 北京燃气科技有限公司</text>
      <text class="copyright-text">版权所有 京ICP备12345678号</text>
      <text class="update-time">最后更新：{{ updateTime }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 响应式数据
const appVersion = ref('1.2.0')
const updateTime = ref('2024-12-15')

const businessList = reactive([
  {
    id: 1,
    name: '设备维护',
    desc: '燃气设备定期检修维护',
    icon: 'gear'
  },
  {
    id: 2,
    name: '安全巡检',
    desc: '燃气管道安全检查',
    icon: 'eye'
  },
  {
    id: 3,
    name: '应急抢修',
    desc: '24小时应急维修服务',
    icon: 'fire'
  },
  {
    id: 4,
    name: '技术培训',
    desc: '专业技术人员培训',
    icon: 'staff'
  },
  {
    id: 5,
    name: '智能监控',
    desc: '物联网设备监控管理',
    icon: 'videocam'
  },
  {
    id: 6,
    name: '数据分析',
    desc: '运营数据分析报告',
    icon: 'bars'
  }
])

// 方法
const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: '************'
  })
}
</script>

<style lang="scss" scoped>
.about-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 32rpx;
}

// 应用信息
.app-info {
  background: linear-gradient(135deg, #4c6ef5 0%, #667eea 100%);
  padding: 80rpx 32rpx 60rpx;
  text-align: center;
  color: white;
}

.app-logo {
  margin-bottom: 24rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.1);
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.app-version {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 16rpx;
}

.app-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.7;
  line-height: 1.4;
}

// 通用区域样式
.company-section,
.business-section,
.contact-section,
.tech-section {
  margin: 32rpx 16rpx 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

// 信息卡片
.info-card,
.contact-card,
.tech-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.info-item,
.contact-item,
.tech-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label,
.contact-label,
.tech-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.info-value,
.contact-value,
.tech-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

// 业务网格
.business-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.business-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.business-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f0f8ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
}

.business-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.business-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

// 联系方式
.contact-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

// 版权信息
.copyright-section {
  text-align: center;
  padding: 60rpx 32rpx 32rpx;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.update-time {
  display: block;
  font-size: 22rpx;
  color: #ccc;
  margin-top: 16rpx;
}
</style>
