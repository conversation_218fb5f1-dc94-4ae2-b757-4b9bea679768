<template>
  <view class="safety-issue-container">
    <!-- 顶部标题栏 -->
    <view class="header-section">
      <view class="header-content">
        <view class="header-left">
          <view class="back-btn" @click="goBack">
            <uni-icons type="back" size="20" color="#fff"></uni-icons>
          </view>
          <text class="header-title">安全环境问题</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
      <!-- 警告提示 -->
      <view class="section-card warning-card">
        <view class="card-body">
          <view class="warning-info">
            <view class="warning-header">
              <uni-icons type="info" size="24" color="#ff6b6b"></uni-icons>
              <text class="warning-title">安全环境不合格</text>
            </view>
            <text class="warning-desc">请详细描述现场发现的安全问题，以便相关人员及时处理。</text>
          </view>
        </view>
      </view>

      <!-- 问题描述 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">问题描述</text>
            <text class="required-mark">*</text>
          </view>
        </view>

        <view class="card-body">
          <textarea
            class="problem-textarea"
            placeholder="请详细描述发现的安全问题，包括具体位置、问题类型、严重程度等..."
            v-model="problemDescription"
            maxlength="500"
            :show-confirm-bar="false"
          />
          <view class="char-count">
            <text class="count-text">{{ problemDescription.length }}/500</text>
          </view>
        </view>
      </view>

      <!-- 问题图片 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">问题图片</text>
            <text class="optional-mark">（选填）</text>
          </view>
        </view>

        <view class="card-body">
          <ImageUpload
            v-model:images="problemImages"
            :max-count="6"
            add-text="添加图片"
            tip-text="最多可上传6张图片，有助于问题的快速定位和处理"
            :show-tip="true"
          />
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <button class="secondary-button" @click="goBack">
          <text class="button-text">取消</text>
        </button>
        <button
          class="main-button submit-btn"
          :class="{ disabled: !canSubmit }"
          @click="submitSafetyIssue"
          :disabled="!canSubmit"
        >
          <text class="button-text">提交问题</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { taskApi } from '@/api/index.js'
import { showToast, showConfirm } from '@/utils/index.js'
import ImageUpload from '@/components/ImageUpload.vue'

// 响应式数据
const taskId = ref('')
const problemDescription = ref('')
const problemImages = ref([])

// 计算属性
const canSubmit = computed(() => {
  return problemDescription.value.trim().length > 0
})

// 页面生命周期
onLoad((options) => {
  taskId.value = options.taskId
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const submitSafetyIssue = async () => {
  if (!canSubmit.value) {
    showToast('请填写问题描述')
    return
  }

  const confirmed = await showConfirm('确认提交安全问题？\n提交后将无法继续作业')
  if (!confirmed) return

  try {
    await taskApi.submitSafetyIssue(taskId.value, {
      description: problemDescription.value.trim(),
      images: problemImages.value
    })
    
    showToast('安全问题已提交')
    
    // 返回任务详情页面
    uni.navigateBack()
  } catch (error) {
    showToast('提交失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
.safety-issue-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

// 顶部区域
.header-section {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%);
  padding: 32rpx 32rpx 24rpx;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -20rpx;
    left: 0;
    right: 0;
    height: 20rpx;
    background: #f8f9fa;
    border-radius: 20rpx 20rpx 0 0;
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.back-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    opacity: 0.7;
  }
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

// 内容滚动区域
.content-scroll {
  flex: 1;
  width: 100%;
  max-width: 100%;
  padding: 24rpx 32rpx 120rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

// 卡片样式
.section-card {
  width: 100%;
  max-width: 100%;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  box-sizing: border-box;

  &.warning-card {
    border: 2rpx solid #ffccc7;
    background: #fff2f0;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.required-mark {
  color: #ff6b6b;
  font-size: 28rpx;
}

.optional-mark {
  color: #8c8c8c;
  font-size: 24rpx;
}

.card-body {
  padding: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

// 警告信息样式
.warning-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.warning-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff6b6b;
}

.warning-desc {
  font-size: 28rpx;
  color: #8c4a4a;
  line-height: 1.6;
}

// 问题描述样式
.problem-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  font-size: 28rpx;
  color: #262626;
  line-height: 1.6;
  box-sizing: border-box;
  resize: none;

  &:focus {
    border-color: #4c6ef5;
    background: #fff;
  }
}

.char-count {
  display: flex;
  justify-content: flex-end;
  margin-top: 12rpx;
}

.count-text {
  font-size: 24rpx;
  color: #8c8c8c;
}

.safe-bottom {
  height: 32rpx;
}

// 底部操作栏
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.main-button, .secondary-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.main-button {
  flex: 2;
  background: linear-gradient(135deg, #ff6b6b, #ff8a8a);
  color: #fff;

  &.disabled {
    background: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
  }

  &::after {
    border: none;
  }
}

.secondary-button {
  flex: 1;
  background: #f8f9fa;
  color: #666;

  &:active {
    background: #e9ecef;
  }
  &::after {
    border: none;
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
}
</style>
