<template>
  <view class="upload-materials-container">
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y="true">
      <!-- 任务信息卡片 -->
      <view class="section-card task-info-card">
        <view class="card-body">
          <view class="task-info-header">
            <view class="task-number">{{ taskDetail.taskNumber }}</view>
            <view class="task-title">{{ taskDetail.title }}</view>
          </view>
        </view>
      </view>
      <!-- 基础资料 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">基础资料</text>
          </view>
          <view class="progress-text">{{ getBasicProgress }}</view>
        </view>

        <view class="card-body">
          <!-- 租户门口照片 -->
          <view class="upload-item">
            <view class="item-header">
              <text class="item-title">租户门口照片</text>
              <text class="required-mark">*</text>
            </view>
            <ImageUpload
              v-model:images="materials.doorPhotos"
              :max-count="6"
              add-text="添加照片"
              tip-text="最多可上传6张图片"
              :show-tip="false"
            />
          </view>



          <!-- 使用场所图片 -->
          <view class="upload-item">
            <view class="item-header">
              <text class="item-title">使用场所图片</text>
              <text class="required-mark">*</text>
            </view>
            <ImageUpload
              v-model:images="materials.usagePlacePhotos"
              :max-count="6"
              add-text="添加照片"
              :show-tip="false"
            />
          </view>

          <!-- 气瓶存放区图片 -->
          <view class="upload-item">
            <view class="item-header">
              <text class="item-title">气瓶存放区图片</text>
              <text class="required-mark">*</text>
            </view>
            <ImageUpload
              v-model:images="materials.storagePhotos"
              :max-count="6"
              add-text="添加照片"
              :show-tip="false"
            />
          </view>
        </view>
      </view>

      <!-- 设备资料 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">设备资料</text>
          </view>
          <view class="progress-text">{{ getEquipmentProgress }}</view>
        </view>

        <view class="card-body">
          <!-- 报警器安装图片 -->
          <view class="upload-item">
            <view class="item-header">
              <text class="item-title">报警器安装图片</text>
              <text class="required-mark">*</text>
            </view>
            <ImageUpload
              v-model:images="materials.alarmPhotos"
              :max-count="6"
              add-text="添加照片"
              :show-tip="false"
            />
          </view>

          <!-- 消防设备图片 -->
          <view class="upload-item">
            <view class="item-header">
              <text class="item-title">消防设备图片</text>
              <text class="required-mark">*</text>
            </view>
            <ImageUpload
              v-model:images="materials.fireEquipmentPhotos"
              :max-count="6"
              add-text="添加照片"
              :show-tip="false"
            />
          </view>

          <!-- 管道阀门安装图 -->
          <view class="upload-item">
            <view class="item-header">
              <text class="item-title">管道阀门安装图</text>
              <text class="required-mark">*</text>
            </view>
            <ImageUpload
              v-model:images="materials.pipelinePhotos"
              :max-count="6"
              add-text="添加照片"
              :show-tip="false"
            />
          </view>

          <!-- 燃气设备使用图 -->
          <view class="upload-item">
            <view class="item-header">
              <text class="item-title">燃气设备使用图</text>
              <text class="required-mark">*</text>
            </view>
            <ImageUpload
              v-model:images="materials.gasEquipmentPhotos"
              :max-count="6"
              add-text="添加照片"
              :show-tip="false"
            />
          </view>
        </view>
      </view>

      <!-- 服务商选择 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">服务商</text>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <view class="service-provider-selector" @tap="showServiceProviderDrawer">
            <text v-if="selectedProvider" class="selected-provider">
              {{ selectedProvider.name }}
            </text>
            <text v-else class="placeholder-text">请选择服务商</text>
            <text class="arrow-down">▼</text>
          </view>
        </view>
      </view>

      <!-- 紧急联系人 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">紧急联系人</text>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <view class="form-item">
            <text class="form-label">联系人姓名</text>
            <input
              class="form-input"
              placeholder="请输入紧急联系人姓名"
              v-model="serviceInfo.emergencyContact.name"
            />
          </view>
          <view class="form-item">
            <text class="form-label">联系电话</text>
            <input
              class="form-input"
              placeholder="请输入联系电话"
              type="number"
              v-model="serviceInfo.emergencyContact.phone"
            />
          </view>
          <view class="form-item">
            <text class="form-label">与用户关系</text>
            <picker
              :range="relationshipOptions"
              :value="relationshipIndex"
              @change="onRelationshipChange"
            >
              <view class="picker-input">
                <text :class="{ placeholder: !serviceInfo.emergencyContact.relationship }">
                  {{ serviceInfo.emergencyContact.relationship || '请选择关系' }}
                </text>
                <text class="arrow-down">▼</text>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 身份证上传 -->
      <view class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">身份证信息</text>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <view class="id-card-section">
            <view class="id-card-item">
              <text class="id-card-label">身份证正面</text>
              <IdCardUploader
                v-model="serviceInfo.idCard.front"
                placeholder="点击拍摄身份证正面"
                :is-back-side="false"
                @update:modelValue="onIdCardFrontChange"
              />
            </view>

            <view class="id-card-item">
              <text class="id-card-label">身份证反面</text>
              <IdCardUploader
                v-model="serviceInfo.idCard.back"
                placeholder="点击拍摄身份证反面"
                :is-back-side="true"
                @update:modelValue="onIdCardBackChange"
              />
            </view>
          </view>
        </view>
      </view>



      <!-- 店铺/企业用户额外字段 -->
      <view v-if="isBusinessUser" class="section-card">
        <view class="card-header">
          <view class="header-left">
            <text class="section-title">企业信息</text>
          </view>
        </view>

        <view class="card-body">


          <!-- 店铺图片 -->
          <view class="form-item">
            <text class="form-label">店铺图片 <text class="required-mark">*</text></text>
            <ImageUpload
              v-model:images="serviceInfo.businessInfo.shopPhotos"
              :max-count="6"
              add-text="添加照片"
              :show-tip="false"
            />
          </view>

          <!-- 营业执照 -->
          <view class="form-item">
            <text class="form-label">营业执照 <text class="required-mark">*</text></text>
            <view class="license-upload" @tap="takeLicensePhoto">
              <view v-if="serviceInfo.businessInfo.licensePhoto" class="license-preview">
                <image :src="serviceInfo.businessInfo.licensePhoto" class="license-image" mode="aspectFit" />
                <view class="license-mask">
                  <text class="retake-text">重新拍摄</text>
                </view>
              </view>
              <view v-else class="license-placeholder">
                <uni-icons type="camera" size="32" color="#999"></uni-icons>
                <text class="placeholder-text">点击拍摄营业执照</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <!-- <view class="progress-info">
        <text class="progress-text">完成度: {{ getTotalProgress }}%</text>
      </view> -->
      <view class="action-buttons">
        <button class="secondary-button" @click="saveDraft">
          <text class="button-text">保存草稿</text>
        </button>
        <button
          class="main-button"
          :class="{ disabled: !canSubmit }"
          @click="submitMaterials"
          :disabled="!canSubmit"
        >
          <text class="button-text">提交信息</text>
        </button>
      </view>
    </view>

    <!-- 服务商选择抽屉 -->
    <uni-popup
      ref="popup"
      type="bottom"
      :safe-area="false"
      @change="onPopupChange"
    >
      <view class="drawer-container">
        <view class="drawer-header">
          <text class="drawer-title">选择服务商</text>
          <button class="drawer-close" @click="hideServiceProviderDrawer">
            <uni-icons type="close" size="20" color="#666"></uni-icons>
          </button>
        </view>

        <scroll-view class="drawer-body" scroll-y>
          <view
            v-for="provider in serviceProviders"
            :key="provider.id"
            class="provider-card"
            :class="{ selected: serviceInfo.serviceProvider === provider.id }"
            @click="selectServiceProvider(provider)"
          >
            <view class="provider-header">
              <text class="provider-name">{{ provider.name }}</text>
              <view v-if="serviceInfo.serviceProvider === provider.id" class="selected-badge">
                <text class="badge-text">已选择</text>
              </view>
            </view>

            <view class="provider-details">
              <view class="detail-item">
                <uni-icons type="location" size="16" color="#666"></uni-icons>
                <text class="detail-text">{{ provider.address }}</text>
              </view>
              <view class="detail-item">
                <uni-icons type="phone" size="16" color="#666"></uni-icons>
                <text class="detail-text">{{ provider.phone }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { taskApi } from '@/api/index.js'
import { showToast, showConfirm } from '@/utils/index.js'
import ImageUpload from '@/components/ImageUpload.vue'
import IdCardUploader from '@/components/IdCardUploader.vue'

// 响应式数据
const taskId = ref('')
const taskDetail = ref({})
const isBusinessUser = ref(true) // 是否为企业用户，实际应该从任务详情中获取

// 资料数据
const materials = ref({
  doorPhotos: [],           // 租户门口照片
  usagePlacePhotos: [],     // 使用场所图片
  storagePhotos: [],        // 气瓶存放区图片
  alarmPhotos: [],          // 报警器安装图片
  fireEquipmentPhotos: [],  // 消防设备图片
  pipelinePhotos: [],       // 管道阀门安装图
  gasEquipmentPhotos: []    // 燃气设备使用图
})

// 服务信息数据
const serviceInfo = ref({
  serviceProvider: '',
  emergencyContact: {
    name: '',
    phone: '',
    relationship: ''
  },
  idCard: {
    front: '',
    back: ''
  },

  businessInfo: {
    shopPhotos: [],
    licensePhoto: ''
  }
})

// 气瓶配送站数据
const serviceProviders = ref([
  {
    id: '1',
    name: '华润燃气配送站',
    address: '北京市朝阳区建国路88号华润大厦1层',
    phone: '************'
  },
  {
    id: '2',
    name: '中石油气瓶配送中心',
    address: '北京市海淀区中关村大街59号中石油大厦',
    phone: '************'
  },
  {
    id: '3',
    name: '新奥燃气配送站',
    address: '北京市西城区金融街35号新奥大厦B座',
    phone: '************'
  },
  {
    id: '4',
    name: '港华燃气配送中心',
    address: '北京市东城区王府井大街138号港华大厦',
    phone: '************'
  }
])

const relationshipOptions = ['本人', '配偶', '子女', '父母', '兄弟姐妹', '朋友', '同事', '其他']
const relationshipIndex = ref(0)

// 抽屉状态
const showDrawer = ref(false)
const popup = ref(null)

// 选中的服务商
const selectedProvider = computed(() => {
  return serviceProviders.value.find(provider => provider.id === serviceInfo.value.serviceProvider)
})

// 计算属性
const getBasicProgress = computed(() => {
  const total = 3 // 基础资料总数
  let completed = 0
  if (materials.value.doorPhotos.length > 0) completed++
  if (materials.value.usagePlacePhotos.length > 0) completed++
  if (materials.value.storagePhotos.length > 0) completed++
  return `${completed}/${total}`
})

const getEquipmentProgress = computed(() => {
  const total = 4 // 设备资料总数
  let completed = 0
  if (materials.value.alarmPhotos.length > 0) completed++
  if (materials.value.fireEquipmentPhotos.length > 0) completed++
  if (materials.value.pipelinePhotos.length > 0) completed++
  if (materials.value.gasEquipmentPhotos.length > 0) completed++
  return `${completed}/${total}`
})

// 服务信息完成度
const getServiceProgress = computed(() => {
  const total = 3
  let completed = 0

  if (serviceInfo.value.serviceProvider) completed++
  if (serviceInfo.value.emergencyContact.name &&
      serviceInfo.value.emergencyContact.phone &&
      serviceInfo.value.emergencyContact.relationship) completed++
  if (serviceInfo.value.idCard.front && serviceInfo.value.idCard.back) completed++

  return `${completed}/${total}`
})

const getTotalProgress = computed(() => {
  const total = 7 // 总资料数
  let completed = 0
  Object.values(materials.value).forEach(photos => {
    if (photos.length > 0) completed++
  })
  return Math.round((completed / total) * 100)
})

const canSubmit = computed(() => {
  // 检查所有必需的资料是否都已上传
  const materialsComplete = Object.values(materials.value).every(photos => photos.length > 0)

  // 检查服务信息是否完整
  const serviceComplete = serviceInfo.value.serviceProvider &&
                         serviceInfo.value.emergencyContact.name &&
                         serviceInfo.value.emergencyContact.phone &&
                         serviceInfo.value.emergencyContact.relationship &&
                         serviceInfo.value.idCard.front &&
                         serviceInfo.value.idCard.back

  // 如果是企业用户，还需要检查企业信息
  const businessComplete = !isBusinessUser.value || (
    serviceInfo.value.businessInfo.shopPhotos.length > 0
  )

  return materialsComplete && serviceComplete && businessComplete
})

// 页面生命周期
onLoad((options) => {
  taskId.value = options.taskId
  loadTaskDetail()
})

// 方法
const loadTaskDetail = async () => {
  // 模拟加载任务详情
  taskDetail.value = {
    taskNumber: `GAS${String(taskId.value).padStart(6, '0')}`,
    title: '燃气设备安装任务'
  }
}



// 保存草稿
const saveDraft = async () => {
  try {
    // 这里应该调用API保存草稿
    showToast('草稿已保存')
  } catch (error) {
    showToast('保存失败')
  }
}

// 显示服务商选择抽屉
const showServiceProviderDrawer = () => {
  console.log('点击了服务商选择器')
  if (popup.value) {
    popup.value.open()
  }
}

// 隐藏服务商选择抽屉
const hideServiceProviderDrawer = () => {
  console.log('关闭服务商抽屉')
  if (popup.value) {
    popup.value.close()
  }
}

// popup状态变化
const onPopupChange = (e) => {
  console.log('popup状态变化:', e)
}

// 服务商选择
const selectServiceProvider = (provider) => {
  serviceInfo.value.serviceProvider = provider.id
  hideServiceProviderDrawer()
}

// 关系选择
const onRelationshipChange = (e) => {
  relationshipIndex.value = e.detail.value
  serviceInfo.value.emergencyContact.relationship = relationshipOptions[e.detail.value]
}

// 身份证上传处理
const onIdCardFrontChange = (value) => {
  serviceInfo.value.idCard.front = value
}

const onIdCardBackChange = (value) => {
  serviceInfo.value.idCard.back = value
}

// 营业执照拍摄
const takeLicensePhoto = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      serviceInfo.value.businessInfo.licensePhoto = res.tempFilePaths[0]
    },
    fail: (err) => {
      console.error('选择营业执照图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}





// 提交资料和服务信息
const submitMaterials = async () => {
  if (!canSubmit.value) {
    showToast('请完成所有必填信息')
    return
  }

  const confirmed = await showConfirm('确认提交所有信息？提交后将进入后台审核流程')
  if (!confirmed) return

  try {
    // 调用API提交资料和服务信息
    await taskApi.uploadMaterials(taskId.value, materials.value)
    await taskApi.submitServiceInfo(taskId.value, serviceInfo.value)
    showToast('信息提交成功，已进入后台审核流程')

    // 跳转回任务详情页面
    uni.redirectTo({
      url: `/pages/task/task-detail?id=${taskId.value}`
    })
  } catch (error) {
    showToast('提交失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
.upload-materials-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

// 任务信息卡片
.task-info-card {
  margin-bottom: 32rpx;
}

.task-info-header {
  margin-bottom: 24rpx;
}

.task-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
}

.task-title {
  font-size: 28rpx;
  color: #8c8c8c;
  line-height: 1.4;
}



// 内容滚动区域
.content-scroll {
  flex: 1;
  padding: 32rpx 32rpx 120rpx;
  width: 100%;
  box-sizing: border-box;
}

// 卡片样式
.section-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}



.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.progress-text {
  font-size: 24rpx;
  color: #4c6ef5;
  font-weight: 500;
}

.card-body {
  padding: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

// 上传项目
.upload-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-title {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
}

.required-mark {
  color: #ff6b6b;
  font-size: 28rpx;
  margin-left: 4rpx;
}



/* 服务商选择器 */
.service-provider-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;

  &:active {
    border-color: #1890ff;
    background: #fff;
    transform: scale(0.98);
  }
}

.selected-provider {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  flex: 1;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
  font-weight: 400;
  flex: 1;
}



// 表单项
.form-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  font-size: 28rpx;
  color: #262626;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:focus {
    border-color: #4c6ef5;
    background: #fff;
  }
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
  box-sizing: border-box;

  &:active {
    border-color: #4c6ef5;
    background: #fff;
  }

  .placeholder {
    color: #999;
  }
}

// 时间设置
.time-setting {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.time-label {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  min-width: 120rpx;
}

.time-picker {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #262626;
  cursor: pointer;
  box-sizing: border-box;

  &:active {
    background: #e9ecef;
  }
}

.time-note {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: #fffbe6;
  border-radius: 12rpx;
  border-left: 4rpx solid #faad14;
}

.note-text {
  font-size: 24rpx;
  color: #8c6e00;
}

// 文本图标样式
.add-icon {
  font-size: 48rpx;
  color: #ccc;
  font-weight: 300;
}

.check-mark {
  font-size: 32rpx;
  color: #4c6ef5;
  font-weight: bold;
}

.arrow-down {
  font-size: 20rpx;
  color: #999;
}

.time-icon {
  font-size: 28rpx;
}

.info-icon {
  font-size: 24rpx;
  color: #faad14;
  font-weight: bold;
}

.calendar-icon {
  font-size: 24rpx;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  cursor: pointer;
  z-index: 10;

  &:active {
    background: rgba(0, 0, 0, 0.8);
  }
}

/* 身份证上传样式 */
.id-card-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.id-card-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.id-card-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.safe-bottom {
  height: 32rpx;
}

// 底部操作栏
.bottom-bar {
  z-index: 2;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  max-width: 100vw;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.progress-info {
  text-align: center;
  margin-bottom: 16rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.main-button, .secondary-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.main-button {
  flex: 2;
  background: linear-gradient(135deg, #4c6ef5, #6c5ce7);
  color: #fff;

  &.disabled {
    background: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
  }

  &::after {
    border: none;
  }
}

.secondary-button {
  flex: 1;
  background: #f8f9fa;
  color: #666;

  &:active {
    background: #e9ecef;
  }
  &::after {
    border: none;
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 抽屉内容样式 */
.drawer-container {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.drawer-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    background: #e8e8e8;
  }
}

.close-icon {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.drawer-body {
  flex: 1;
  padding: 24rpx 32rpx 32rpx;
}

.provider-card {
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  margin-bottom: 16rpx;
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    transform: scale(0.98);
  }

  &.selected {
    border-color: #1890ff;
    background: #f0f4ff;
  }
}

.provider-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.provider-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
}

.selected-badge {
  padding: 4rpx 12rpx;
  background: #1890ff;
  border-radius: 12rpx;
}

.badge-text {
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
}

.provider-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 营业执照上传样式 */
.license-upload {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.license-placeholder {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.2s ease;

  &:active {
    border-color: #1890ff;
    background: #f0f4ff;
  }
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
}

.license-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.license-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.license-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;

  .license-upload:active & {
    opacity: 1;
  }
}

.retake-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}
</style>
