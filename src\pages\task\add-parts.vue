<template>
  <view class="page-container">
    <!-- 内容区域 -->
    <scroll-view class="content-section" scroll-y>
      <!-- 耗材列表 -->
      <view class="parts-list">
        <view
          v-for="(part, key) in partsList"
          :key="key"
          class="parts-item"
          :class="{ active: part.quantity > 0 }"
        >
          <!-- 耗材基本信息 -->
          <view class="item-main">
            <view class="item-info">
              <view class="item-content">
                <text class="item-name">{{ part.name }}</text>
                <text class="item-desc">{{ getPartDesc(key) }}</text>
              </view>
            </view>

            <view class="quantity-control">
              <button
                class="quantity-btn minus"
                @click="decreaseQuantity(key)"
                :disabled="part.quantity <= 0"
              >
                <text class="btn-icon">−</text>
              </button>
              <text class="quantity-num">{{ part.quantity }}</text>
              <button
                class="quantity-btn plus"
                @click="increaseQuantity(key)"
                :disabled="part.quantity >= 99"
              >
                <text class="btn-icon">+</text>
              </button>
            </view>
          </view>

          <!-- 二维码扫描区域 -->
          <view v-if="part.needQR && part.quantity > 0" class="scan-section">
            <view class="scan-header">
              <text class="scan-label">设备二维码</text>
              <text class="scan-progress">{{ part.qrCodes.length }}/{{ part.quantity }}</text>
            </view>

            <!-- 已扫描的二维码 -->
            <view v-if="part.qrCodes.length > 0" class="scanned-list">
              <view
                v-for="(qrCode, index) in part.qrCodes"
                :key="index"
                class="scanned-item"
              >
                <text class="qr-text">{{ qrCode }}</text>
                <button class="delete-btn" @click="deleteQRCode(key, index)">
                  <text class="delete-icon">×</text>
                </button>
              </view>
            </view>

            <!-- 扫描按钮 -->
            <button
              v-if="part.qrCodes.length < part.quantity"
              class="scan-btn"
              @click="scanQRCode(key)"
            >
              <text class="scan-text">扫描二维码 (还需{{ part.quantity - part.qrCodes.length }}个)</text>
            </button>

            <!-- 扫描完成提示 -->
            <view v-if="part.qrCodes.length === part.quantity" class="scan-complete">
              <text class="complete-text">✓ 已完成所有设备扫描</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="content-bottom-safe"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <button 
          class="main-button submit-btn" 
          :disabled="!canSubmit"
          @click="submitParts"
        >
          <text class="button-text">添加耗材</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { taskApi } from '@/api/index.js'
import { showToast, showConfirm, scanCode } from '@/utils/index'

// 响应式数据
const taskId = ref('')
const taskDetail = ref({})

// 耗材列表数据
const partsList = ref({
  pipe: {
    name: '燃气管',
    quantity: 0,
    needQR: false
  },
  homeAlarm: {
    name: '家用报警器',
    quantity: 0,
    needQR: true,
    qrCodes: []
  },
  businessAlarm: {
    name: '商用报警器',
    quantity: 0,
    needQR: true,
    qrCodes: []
  },
  coAlarm: {
    name: '一氧化碳报警器',
    quantity: 0,
    needQR: true,
    qrCodes: []
  },
  pressureValve: {
    name: '减压阀',
    quantity: 0,
    needQR: false
  },
  mediumValve: {
    name: '中压阀',
    quantity: 0,
    needQR: false
  }
})

// 计算属性
const canSubmit = computed(() => {
  // 检查是否有选择的耗材
  const hasSelectedParts = Object.values(partsList.value).some(part => part.quantity > 0)

  if (!hasSelectedParts) return false

  // 检查需要二维码的耗材是否都已扫描
  for (const [key, part] of Object.entries(partsList.value)) {
    if (part.needQR && part.quantity > 0) {
      if (part.qrCodes.length < part.quantity) {
        return false
      }
    }
  }

  return true
})

// 页面加载
onLoad((options) => {
  if (options.taskId) {
    taskId.value = options.taskId
    loadTaskDetail()
  }
})

// 加载任务详情
const loadTaskDetail = async () => {
  try {
    const response = await taskApi.getTaskDetail(taskId.value)
    taskDetail.value = response.data
  } catch (error) {
    console.error('加载任务详情失败:', error)
    showToast('加载任务详情失败')
  }
}



// 获取耗材描述
const getPartDesc = (key) => {
  const descriptions = {
    pipe: '燃气管道及配件',
    homeAlarm: '家庭燃气泄漏报警器',
    businessAlarm: '商业场所燃气报警器',
    coAlarm: '一氧化碳泄漏检测报警器',
    pressureValve: '燃气减压调节阀',
    mediumValve: '中压燃气调节阀'
  }
  return descriptions[key] || '耗材配件'
}

// 数量操作
const increaseQuantity = (type) => {
  if (partsList.value[type].quantity < 99) {
    partsList.value[type].quantity++
  }
}

const decreaseQuantity = (type) => {
  if (partsList.value[type].quantity > 0) {
    partsList.value[type].quantity--

    // 如果是报警器类型，需要调整二维码数组
    if (partsList.value[type].needQR) {
      const currentQRCount = partsList.value[type].qrCodes.length
      const newQuantity = partsList.value[type].quantity

      // 如果二维码数量超过了新的数量，删除多余的二维码
      if (currentQRCount > newQuantity) {
        partsList.value[type].qrCodes = partsList.value[type].qrCodes.slice(0, newQuantity)
      }
    }
  }
}

// 扫描二维码
const scanQRCode = async (type) => {
  try {
    const result = await scanCode()

    // 检查是否已经扫描过相同的二维码
    if (partsList.value[type].qrCodes.includes(result)) {
      showToast('该二维码已扫描过')
      return
    }

    partsList.value[type].qrCodes.push(result)
    showToast('扫描成功')
  } catch (error) {
    console.error('扫描失败:', error)
    showToast('扫描失败，请重试')
  }
}

// 删除二维码
const deleteQRCode = (type, index) => {
  partsList.value[type].qrCodes.splice(index, 1)
}

// 检查未完成扫描的报警器
const getIncompleteAlarms = () => {
  const incomplete = []
  Object.entries(partsList.value).forEach(([key, part]) => {
    if (part.needQR && part.quantity > 0 && part.qrCodes.length < part.quantity) {
      const missing = part.quantity - part.qrCodes.length
      incomplete.push(`${part.name}还需扫描${missing}个设备`)
    }
  })
  return incomplete
}

// 提交耗材
const submitParts = async () => {
  // 检查是否有选择耗材
  const hasSelectedParts = Object.values(partsList.value).some(part => part.quantity > 0)
  if (!hasSelectedParts) {
    showToast('请选择要添加的耗材')
    return
  }

  // 检查报警器扫描情况
  const incompleteAlarms = getIncompleteAlarms()
  if (incompleteAlarms.length > 0) {
    showToast(incompleteAlarms[0]) // 显示第一个未完成的提示
    return
  }

  const confirmed = await showConfirm('确认添加耗材？')
  if (!confirmed) return

  try {
    // 构建提交数据
    const submitData = []

    Object.entries(partsList.value).forEach(([key, part]) => {
      if (part.quantity > 0) {
        submitData.push({
          type: part.name,
          quantity: part.quantity,
          qrCodes: part.qrCodes || []
        })
      }
    })

    await taskApi.addParts(taskId.value, submitData)
    showToast('耗材添加成功')

    // 返回上一页
    uni.navigateBack()
  } catch (error) {
    console.error('添加耗材失败:', error)
    showToast('添加耗材失败')
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  width: 100vw;
  overflow-x: hidden;
}

/* 内容区域 */
.content-section {
  flex: 1;
  padding: 24rpx 20rpx 0;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 耗材列表 */
.parts-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 耗材项目 */
.parts-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;

  &.active {
    border-color: #1890ff;
    box-shadow: 0 4rpx 20rpx rgba(24, 144, 255, 0.1);
  }
}

/* 主要内容区域 */
.item-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-content {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
  margin-bottom: 4rpx;
  display: block;
}

.item-desc {
  font-size: 22rpx;
  color: #8c8c8c;
  line-height: 1.2;
  display: block;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: 1rpx solid #d9d9d9;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &.minus {
    &:active:not(:disabled) {
      background: #ff4d4f;
      border-color: #ff4d4f;

      .btn-icon {
        color: #fff;
      }
    }
  }

  &.plus {
    &:active:not(:disabled) {
      background: #52c41a;
      border-color: #52c41a;

      .btn-icon {
        color: #fff;
      }
    }
  }

  &:disabled {
    background: #f5f5f5;
    border-color: #f0f0f0;

    .btn-icon {
      color: #ccc;
    }
  }
}

.btn-icon {
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  transition: color 0.2s ease;
}

.quantity-num {
  min-width: 48rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
}

/* 扫描区域 */
.scan-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.scan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.scan-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.scan-progress {
  font-size: 22rpx;
  color: #1890ff;
  font-weight: 600;
  background: #e6f7ff;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

/* 已扫描列表 */
.scanned-list {
  margin-bottom: 12rpx;
}

.scanned-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 12rpx;
  background: #f6ffed;
  border-radius: 6rpx;
  margin-bottom: 6rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.qr-text {
  font-size: 22rpx;
  color: #52c41a;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  flex: 1;
  word-break: break-all;
}

.delete-btn {
  width: 32rpx;
  height: 32rpx;
  min-width: 32rpx;
  min-height: 32rpx;
  border-radius: 50%;
  background: #ff4d4f;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  flex-shrink: 0;
  padding: 0;
  box-sizing: border-box;

  &:active {
    background: #ff7875;
  }
}

.delete-icon {
  font-size: 18rpx;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}

/* 扫描按钮 */
.scan-btn {
  width: 100%;
  height: 64rpx;
  background: #f8f9fa;
  border: 1rpx dashed #d9d9d9;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:active {
    background: #e6f7ff;
    border-color: #1890ff;
    transform: scale(0.98);
  }
}

.scan-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 扫描完成提示 */
.scan-complete {
  width: 100%;
  height: 64rpx;
  background: #f6ffed;
  border: 1rpx solid #b7eb8f;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.complete-text {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}



/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, #fff 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(240, 240, 240, 0.8);
  padding: 32rpx 24rpx;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.12);
  z-index: 100;

  /* 适配不同平台的底部安全区域 */
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));

  /* H5 平台额外底部间距 */
  /* #ifdef H5 */
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom) + 20rpx);
  /* #endif */

  /* 小程序平台底部间距 */
  /* #ifdef MP */
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom) + 10rpx);
  /* #endif */
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.main-button {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: scale(0.96);

    &::before {
      opacity: 1;
    }
  }

  &:disabled {
    opacity: 0.6;
    transform: none !important;
    background: linear-gradient(135deg, #d9d9d9, #bfbfbf);
    box-shadow: none;

    &::before {
      display: none;
    }
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
}

/* 内容区域底部安全间距 */
.content-bottom-safe {
  /* 基础高度：底部操作栏高度 + 额外间距 */
  height: calc(96rpx + 64rpx + env(safe-area-inset-bottom));

  /* H5 平台额外间距 */
  /* #ifdef H5 */
  height: calc(96rpx + 84rpx + env(safe-area-inset-bottom));
  /* #endif */

  /* 小程序平台间距 */
  /* #ifdef MP */
  height: calc(96rpx + 74rpx + env(safe-area-inset-bottom));
  /* #endif */

  /* App 平台间距 */
  /* #ifdef APP-PLUS */
  height: calc(96rpx + 64rpx + env(safe-area-inset-bottom));
  /* #endif */
}
</style>
