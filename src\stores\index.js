// 简单的状态管理，不依赖外部库
class Store {
  constructor() {
    this.state = {
      // 用户信息
      user: {
        id: null,
        name: '',
        phone: '',
        role: '', // 'delivery', 'maintenance', 'inspector'
        avatar: '',
        workNumber: '',
        isLoggedIn: false
      },
      
      // 任务相关
      tasks: {
        list: [],
        currentTask: null,
        filters: {
          status: 'all', // 'new', 'in-progress', 'completed', 'all'
          priority: 'all'
        }
      },
      
      // 系统设置
      settings: {
        theme: 'light',
        notifications: true,
        autoLocation: true
      },
      
      // 消息中心
      messages: {
        list: [],
        unreadCount: 0
      }
    }
    
    this.listeners = []
  }
  
  // 订阅状态变化
  subscribe(listener) {
    this.listeners.push(listener)
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }
  
  // 通知所有监听器
  notify() {
    this.listeners.forEach(listener => listener(this.state))
  }
  
  // 更新状态
  setState(updates) {
    this.state = { ...this.state, ...updates }
    this.notify()
  }
  
  // 用户相关操作
  setUser(user) {
    this.setState({
      user: { ...this.state.user, ...user }
    })
  }
  
  login(userInfo) {
    this.setUser({ ...userInfo, isLoggedIn: true })
    // 保存到本地存储
    uni.setStorageSync('userInfo', { ...userInfo, isLoggedIn: true })
  }
  
  logout() {
    this.setUser({
      id: null,
      name: '',
      phone: '',
      role: '',
      avatar: '',
      workNumber: '',
      isLoggedIn: false
    })
    uni.removeStorageSync('userInfo')
  }
  
  // 任务相关操作
  setTasks(tasks) {
    this.setState({
      tasks: { ...this.state.tasks, list: tasks }
    })
  }
  
  setCurrentTask(task) {
    this.setState({
      tasks: { ...this.state.tasks, currentTask: task }
    })
  }
  
  updateTaskStatus(taskId, status) {
    const tasks = this.state.tasks.list.map(task => 
      task.id === taskId ? { ...task, status } : task
    )
    this.setTasks(tasks)
  }
  
  // 消息相关操作
  addMessage(message) {
    const messages = [...this.state.messages.list, message]
    this.setState({
      messages: {
        list: messages,
        unreadCount: this.state.messages.unreadCount + 1
      }
    })
  }
  
  markAllMessagesRead() {
    this.setState({
      messages: {
        ...this.state.messages,
        unreadCount: 0
      }
    })
  }
  
  // 初始化，从本地存储恢复状态
  init() {
    try {
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo && userInfo.isLoggedIn) {
        this.setUser(userInfo)
      }
    } catch (e) {
      console.error('Failed to load user info from storage:', e)
    }
  }
}

// 创建全局store实例
const store = new Store()

export default store
