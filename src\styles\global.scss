/* 全局样式 - 燃气管理系统 */

/* ==================== 重置样式 ==================== */
* {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.4;
  color: #212121;
}

/* ==================== 布局类 ==================== */
.container {
  padding: 0 $uni-spacing-base;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* ==================== 文字类 ==================== */
.text-xs { font-size: $uni-font-size-xs; }
.text-sm { font-size: $uni-font-size-sm; }
.text-base { font-size: $uni-font-size-base; }
.text-lg { font-size: $uni-font-size-lg; }
.text-xl { font-size: $uni-font-size-xl; }
.text-2xl { font-size: $uni-font-size-2xl; }

.text-primary { color: $uni-color-primary; }
.text-success { color: $uni-color-success; }
.text-warning { color: $uni-color-warning; }
.text-error { color: $uni-color-error; }
.text-info { color: $uni-color-info; }

.text-secondary { color: #757575; }
.text-tertiary { color: #9E9E9E; }
.text-disabled { color: #BDBDBD; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: normal; }

/* ==================== 间距类 ==================== */
.m-xs { margin: $uni-spacing-xs; }
.m-sm { margin: $uni-spacing-sm; }
.m-base { margin: $uni-spacing-base; }
.m-lg { margin: $uni-spacing-lg; }
.m-xl { margin: $uni-spacing-xl; }

.mt-xs { margin-top: $uni-spacing-xs; }
.mt-sm { margin-top: $uni-spacing-sm; }
.mt-base { margin-top: $uni-spacing-base; }
.mt-lg { margin-top: $uni-spacing-lg; }
.mt-xl { margin-top: $uni-spacing-xl; }

.mb-xs { margin-bottom: $uni-spacing-xs; }
.mb-sm { margin-bottom: $uni-spacing-sm; }
.mb-base { margin-bottom: $uni-spacing-base; }
.mb-lg { margin-bottom: $uni-spacing-lg; }
.mb-xl { margin-bottom: $uni-spacing-xl; }

.ml-xs { margin-left: $uni-spacing-xs; }
.ml-sm { margin-left: $uni-spacing-sm; }
.ml-base { margin-left: $uni-spacing-base; }
.ml-lg { margin-left: $uni-spacing-lg; }

.mr-xs { margin-right: $uni-spacing-xs; }
.mr-sm { margin-right: $uni-spacing-sm; }
.mr-base { margin-right: $uni-spacing-base; }
.mr-lg { margin-right: $uni-spacing-lg; }

.p-xs { padding: $uni-spacing-xs; }
.p-sm { padding: $uni-spacing-sm; }
.p-base { padding: $uni-spacing-base; }
.p-lg { padding: $uni-spacing-lg; }
.p-xl { padding: $uni-spacing-xl; }

.pt-xs { padding-top: $uni-spacing-xs; }
.pt-sm { padding-top: $uni-spacing-sm; }
.pt-base { padding-top: $uni-spacing-base; }
.pt-lg { padding-top: $uni-spacing-lg; }

.pb-xs { padding-bottom: $uni-spacing-xs; }
.pb-sm { padding-bottom: $uni-spacing-sm; }
.pb-base { padding-bottom: $uni-spacing-base; }
.pb-lg { padding-bottom: $uni-spacing-lg; }

.pl-xs { padding-left: $uni-spacing-xs; }
.pl-sm { padding-left: $uni-spacing-sm; }
.pl-base { padding-left: $uni-spacing-base; }
.pl-lg { padding-left: $uni-spacing-lg; }

.pr-xs { padding-right: $uni-spacing-xs; }
.pr-sm { padding-right: $uni-spacing-sm; }
.pr-base { padding-right: $uni-spacing-base; }
.pr-lg { padding-right: $uni-spacing-lg; }

/* ==================== 背景类 ==================== */
.bg-primary { background-color: $uni-color-primary; }
.bg-success { background-color: $uni-color-success; }
.bg-warning { background-color: $uni-color-warning; }
.bg-error { background-color: $uni-color-error; }
.bg-white { background-color: #ffffff; }
.bg-gray { background-color: #f5f5f5; }

/* ==================== 边框类 ==================== */
.border { border: 1rpx solid $uni-border-color; }
.border-top { border-top: 1rpx solid $uni-border-color; }
.border-bottom { border-bottom: 1rpx solid $uni-border-color; }
.border-left { border-left: 1rpx solid $uni-border-color; }
.border-right { border-right: 1rpx solid $uni-border-color; }

.rounded-xs { border-radius: $uni-border-radius-xs; }
.rounded-sm { border-radius: $uni-border-radius-sm; }
.rounded-base { border-radius: $uni-border-radius-base; }
.rounded-lg { border-radius: $uni-border-radius-lg; }
.rounded-xl { border-radius: $uni-border-radius-xl; }
.rounded-full { border-radius: 50%; }

/* ==================== 阴影类 ==================== */
.shadow-sm { box-shadow: $shadow-sm; }
.shadow-base { box-shadow: $shadow-base; }
.shadow-lg { box-shadow: $shadow-lg; }

/* ==================== 状态类 ==================== */
.status-new { color: $status-new; }
.status-progress { color: $status-progress; }
.status-completed { color: $status-completed; }
.status-cancelled { color: $status-cancelled; }

.priority-low { color: $priority-low; }
.priority-normal { color: $priority-normal; }
.priority-high { color: $priority-high; }
.priority-urgent { color: $priority-urgent; }

/* ==================== 简约卡片组件 ==================== */
.card {
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  margin-bottom: $card-margin;
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: $card-shadow-hover;
    transform: translateY(-2rpx);
  }

  &:active {
    box-shadow: $card-shadow-active;
    transform: translateY(-1rpx);
  }
}

.card-elevated {
  box-shadow: $shadow-base;

  &:hover {
    box-shadow: $shadow-lg;
  }
}

.card-flat {
  box-shadow: none;
  border: 1rpx solid #e9ecef;

  &:hover {
    border-color: $uni-color-primary;
    box-shadow: $shadow-xs;
  }
}

.card-interactive {
  cursor: pointer;

  &:hover {
    box-shadow: $shadow-base;
    transform: translateY(-2rpx);
  }
}

.card-compact {
  padding: 20rpx;
  margin-bottom: 12rpx;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 $uni-spacing-lg;
  height: $button-height-base;
  border-radius: $uni-border-radius-base;
  font-size: $uni-font-size-base;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.btn-primary {
    background-color: $uni-color-primary;
    color: white;
    
    &:hover {
      background-color: $uni-color-primary-dark;
    }
  }
  
  &.btn-secondary {
    background-color: #f5f5f5;
    color: $uni-text-color;
    
    &:hover {
      background-color: #e0e0e0;
    }
  }
  
  &.btn-sm {
    height: $button-height-sm;
    padding: 0 $uni-spacing-base;
    font-size: $uni-font-size-sm;
  }
  
  &.btn-lg {
    height: $button-height-lg;
    padding: 0 $uni-spacing-xl;
    font-size: $uni-font-size-lg;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.input {
  width: 100%;
  height: $input-height;
  padding: 0 $uni-spacing-base;
  border: 1rpx solid $uni-border-color;
  border-radius: $uni-border-radius-base;
  font-size: $uni-font-size-base;
  background-color: white;
  
  &:focus {
    border-color: $uni-color-primary;
    outline: none;
  }
  
  &::placeholder {
    color: $uni-text-color-placeholder;
  }
}

/* ==================== 动画类 ==================== */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 响应式 ==================== */
@media screen and (max-width: 750rpx) {
  .container {
    padding: 0 $uni-spacing-sm;
  }
}
