<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input 
          class="search-input" 
          placeholder="搜索客户姓名、电话或地址"
          v-model="searchKeyword"
          @input="handleSearch"
        />
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{ filteredUsers.length }}</text>
        <text class="stat-label">总客户</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ servedUsersCount }}</text>
        <text class="stat-label">已服务</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ unservedUsersCount }}</text>
        <text class="stat-label">未服务</text>
      </view>
    </view>

    <!-- 用户列表 -->
    <view class="user-list">
      <view 
        class="user-card" 
        v-for="user in filteredUsers" 
        :key="user.id"
        @click="viewUserDetail(user)"
      >
        <view class="user-header">
          <view class="user-info">
            <text class="user-name">{{ user.name }}</text>
            <view class="user-tags">
              <text class="gas-type-tag" :class="'type-' + user.gasType">{{ getGasTypeText(user.gasType) }}</text>
              <text class="service-status" :class="user.isServed ? 'served' : 'unserved'">
                {{ user.isServed ? '已服务' : '未服务' }}
              </text>
            </view>
          </view>
          <view class="user-actions">
            <view class="action-btn" @click.stop="callUser(user)">
              <uni-icons type="phone" size="16" color="#4c6ef5"></uni-icons>
            </view>
          </view>
        </view>

        <view class="user-details">
          <view class="detail-item">
            <uni-icons type="location" size="14" color="#666"></uni-icons>
            <text class="detail-text">{{ user.address }}</text>
          </view>
          <view class="detail-item">
            <uni-icons type="phone" size="14" color="#666"></uni-icons>
            <text class="detail-text">{{ user.phone }}</text>
          </view>
          <view class="detail-item">
            <uni-icons type="shop" size="14" color="#666"></uni-icons>
            <text class="detail-text">{{ user.serviceProvider }}</text>
          </view>
        </view>

        <view class="user-footer" v-if="user.lastServiceDate">
          <text class="last-service">上次服务：{{ formatDate(user.lastServiceDate) }}</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredUsers.length === 0">
        <uni-icons type="person" size="60" color="#d9d9d9"></uni-icons>
        <text class="empty-text">暂无客户数据</text>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom" :mask-click="false">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <view class="modal-close" @click="closeFilterModal">
            <uni-icons type="closeempty" size="20" color="#666"></uni-icons>
          </view>
        </view>

        <view class="filter-content">
          <view class="filter-group">
            <text class="filter-label">用气类型</text>
            <view class="filter-options">
              <view 
                class="filter-option" 
                :class="{ active: selectedGasType === type.value }"
                v-for="type in gasTypes" 
                :key="type.value"
                @click="selectedGasType = type.value"
              >
                <text class="option-text">{{ type.label }}</text>
              </view>
            </view>
          </view>

          <view class="filter-group">
            <text class="filter-label">服务状态</text>
            <view class="filter-options">
              <view 
                class="filter-option" 
                :class="{ active: selectedServiceStatus === status.value }"
                v-for="status in serviceStatuses" 
                :key="status.value"
                @click="selectedServiceStatus = status.value"
              >
                <text class="option-text">{{ status.label }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-actions">
          <button class="reset-btn" @click="resetFilters">重置</button>
          <button class="confirm-btn" @click="applyFilters">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'

// 响应式数据
const searchKeyword = ref('')
const isFilterModalVisible = ref(false)
const selectedGasType = ref('all')
const selectedServiceStatus = ref('all')
const users = ref([])

const gasTypes = reactive([
  { value: 'all', label: '全部' },
  { value: 'residential', label: '居民用户' },
  { value: 'mobile', label: '移动用户' },
  { value: 'commercial', label: '店铺/企业用户' }
])

const serviceStatuses = reactive([
  { value: 'all', label: '全部' },
  { value: 'served', label: '已服务' },
  { value: 'unserved', label: '未服务' }
])

// 计算属性
const filteredUsers = computed(() => {
  let result = users.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(user =>
      user.name.toLowerCase().includes(keyword) ||
      user.phone.includes(keyword) ||
      user.address.toLowerCase().includes(keyword)
    )
  }

  // 用气类型过滤
  if (selectedGasType.value !== 'all') {
    result = result.filter(user => user.gasType === selectedGasType.value)
  }

  // 服务状态过滤
  if (selectedServiceStatus.value !== 'all') {
    const isServed = selectedServiceStatus.value === 'served'
    result = result.filter(user => user.isServed === isServed)
  }

  return result
})

const servedUsersCount = computed(() => {
  return users.value.filter(user => user.isServed).length
})

const unservedUsersCount = computed(() => {
  return users.value.filter(user => !user.isServed).length
})

// 生命周期钩子
onMounted(() => {
  loadUsers()
})

// 页面生命周期 - 下拉刷新
uni.onPullDownRefresh = () => {
  loadUsers()
  setTimeout(() => {
    uni.stopPullDownRefresh()
  }, 1000)
}

// 方法
const loadUsers = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    users.value = [
          {
            id: 1,
            name: '张先生',
            gasType: 'residential',
            address: '朝阳区建国路88号华贸中心1-2-301',
            phone: '13812341234',
            serviceProvider: '北京燃气配送有限公司',
            isServed: true,
            lastServiceDate: new Date('2024-12-10')
          },
          {
            id: 2,
            name: '李女士',
            gasType: 'commercial',
            address: '海淀区中关村大街1号中关村广场B座',
            phone: '13956785678',
            serviceProvider: '海淀区燃气服务中心',
            isServed: false,
            lastServiceDate: null
          },
          {
            id: 3,
            name: '王总',
            gasType: 'commercial',
            address: '丰台区南三环西路16号搜宝商务中心',
            phone: '13690129012',
            serviceProvider: '丰台燃气工程公司',
            isServed: true,
            lastServiceDate: new Date('2024-12-08')
          },
          {
            id: 4,
            name: '赵女士',
            gasType: 'residential',
            address: '西城区西单北大街133号西单商场',
            phone: '13734563456',
            serviceProvider: '西城燃气服务站',
            isServed: false,
            lastServiceDate: null
          },
          {
            id: 5,
            name: '陈先生',
            gasType: 'mobile',
            address: '东城区王府井大街255号王府井百货',
            phone: '13578907890',
            serviceProvider: '东城区燃气配送中心',
            isServed: true,
            lastServiceDate: new Date('2024-12-05')
          }
        ]
    } catch (error) {
      console.error('加载用户数据失败:', error)
      uni.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  }

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

// 查看用户详情
const viewUserDetail = (user) => {
  uni.navigateTo({
    url: `/pages/users/user-detail?id=${user.id}`
  })
}

// 拨打电话
const callUser = (user) => {
  uni.makePhoneCall({
    phoneNumber: user.phone
  })
}

// 获取用气类型文本
const getGasTypeText = (type) => {
  const typeMap = {
    'residential': '居民用户',
    'mobile': '移动用户',
    'commercial': '店铺/企业用户'
  }
  return typeMap[type] || '未知'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${month}月${day}日`
}

// 筛选弹窗引用
const filterPopup = ref(null)

// 显示筛选弹窗
const showFilterModal = () => {
  filterPopup.value.open()
}

// 关闭筛选弹窗
const closeFilterModal = () => {
  filterPopup.value.close()
}

// 重置筛选条件
const resetFilters = () => {
  selectedGasType.value = 'all'
  selectedServiceStatus.value = 'all'
}

// 应用筛选条件
const applyFilters = () => {
  closeFilterModal()
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f8f9fa;
}

// 搜索区域
.search-section {
  background: white;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 24rpx;
  height: 72rpx;
}

.search-input {
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}

// 统计区域
.stats-section {
  display: flex;
  background: white;
  margin: 16rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.stat-item {
  flex: 1;
  text-align: center;

  &:not(:last-child) {
    border-right: 1rpx solid #f0f0f0;
  }
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #4c6ef5;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

// 用户列表
.user-list {
  padding: 0 16rpx 32rpx;
}

.user-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.user-tags {
  display: flex;
  gap: 12rpx;
}

.gas-type-tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;

  &.type-residential {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.type-mobile {
    background: #f9f0ff;
    color: #722ed1;
  }

  &.type-commercial {
    background: #f6ffed;
    color: #52c41a;
  }
}

.service-status {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;

  &.served {
    background: #f6ffed;
    color: #52c41a;
  }

  &.unserved {
    background: #fff1f0;
    color: #ff4d4f;
  }
}

.user-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f0f8ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.detail-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.user-footer {
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.last-service {
  font-size: 24rpx;
  color: #999;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 16rpx;
}

// 筛选弹窗
.filter-modal {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  max-height: 80vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-content {
  margin-bottom: 32rpx;
}

.filter-group {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  border-radius: 50rpx;
  background: #f5f5f5;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;

  &.active {
    background: #e6f7ff;
    border-color: #4c6ef5;
  }
}

.option-text {
  font-size: 26rpx;
  color: #666;
}

.filter-option.active .option-text {
  color: #4c6ef5;
  font-weight: 500;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.reset-btn, .confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #4c6ef5;
  color: white;
}
</style>
