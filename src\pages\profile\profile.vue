<template>
  <view class="profile-container">
    <!-- 用户信息头部 -->
    <view class="profile-header">
      <view class="user-info">
        <image 
          class="avatar" 
          :src="userInfo.avatar || '/static/default-avatar.png'" 
          mode="aspectFill"
          @click="changeAvatar"
        />
        <view class="user-details">
          <text class="username">{{ userInfo.name || '未设置姓名' }}</text>
          <text class="work-number">工号：{{ userInfo.workNumber || '未设置' }}</text>
          <text class="role-text">{{ getRoleText(userInfo.role) }}</text>
        </view>
        <view class="edit-btn" @click="editProfile">
          <uni-icons type="compose" size="18" color="white"></uni-icons>
        </view>
      </view>
      

    </view>
    
    <!-- 数据统计 -->
    <view class="stats-section">
      <view class="section-title">本月数据</view>
      <view class="stats-grid">
        <view class="stat-item" v-for="stat in statsData" :key="stat.key">
          <text class="stat-number">{{ stat.value }}</text>
          <text class="stat-label">{{ stat.label }}</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group" v-for="group in menuGroups" :key="group.title">
        <view class="group-title">{{ group.title }}</view>
        <view class="menu-list">
          <view 
            class="menu-item"
            v-for="item in group.items"
            :key="item.key"
            @click="handleMenuClick(item)"
          >
            <view class="menu-icon">
              <uni-icons :type="item.icon" size="20" color="#666"></uni-icons>
            </view>
            <text class="menu-label">{{ item.label }}</text>
            <view class="menu-extra">
              <text v-if="item.badge" class="badge">{{ item.badge }}</text>
              <uni-icons type="right" size="14" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="handleLogout">
        退出登录
      </button>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import store from '@/stores/index.js'
import { chooseImage, showToast, showConfirm } from '@/utils/index.js'

// 响应式数据
const statsData = ref([
  { key: 'completed', label: '已完成', value: 28 },
  { key: 'inProgress', label: '进行中', value: 3 },
  { key: 'workHours', label: '工作时长', value: '186h' }
])

// 计算属性
const userInfo = computed(() => {
  return store.state.user
})

const unreadCount = computed(() => {
  return store.state.messages.unreadCount
})

// 菜单组数据 - 使用计算属性以便响应 unreadCount 变化
const menuGroups = computed(() => [
  {
    title: '工作管理',
    items: [
      { key: 'myTasks', label: '我的工单', icon: 'list', iconType: 'uni-icons' }
    ]
  },
  {
    title: '消息通知',
    items: [
      { key: 'messages', label: '消息中心', icon: 'chat', iconType: 'uni-icons', badge: unreadCount.value > 0 ? unreadCount.value : null },
      { key: 'notifications', label: '系统通知', icon: 'notification', iconType: 'uni-icons' },
      { key: 'announcements', label: '公告通知', icon: 'sound', iconType: 'uni-icons' }
    ]
  },
  {
    title: '工具与支持',
    items: [
      { key: 'manuals', label: '技术手册', icon: 'paperplane', iconType: 'uni-icons' },
      { key: 'help', label: '帮助中心', icon: 'help', iconType: 'uni-icons' }
    ]
  },
  {
    title: '设置',
    items: [
      { key: 'about', label: '关于我们', icon: 'info', iconType: 'uni-icons' }
    ]
  }
])

// 生命周期钩子
onLoad(() => {
  loadUserData()
})

onShow(() => {
  loadUserData()
})
// 加载用户数据
const loadUserData = async () => {
  try {
    // 这里可以从API获取最新的用户数据和统计信息
    // const userData = await userApi.getUserInfo()
    // 更新统计数据等
  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

// 获取角色文本
const getRoleText = (role) => {
  const roleMap = {
    maintenance: '维保员',
    inspector: '巡检员',
    delivery: '配送员'
  }
  return roleMap[role] || '未设置角色'
}

// 更换头像
const changeAvatar = async () => {
  try {
    const images = await chooseImage(1)
    if (images && images.length > 0) {
      // 上传头像
      showToast('头像上传功能开发中...')
      // const avatarUrl = await userApi.uploadAvatar(images[0])
      // store.setUser({ avatar: avatarUrl })
    }
  } catch (error) {
    showToast('选择图片失败')
  }
}

// 编辑个人资料
const editProfile = () => {
  uni.navigateTo({
    url: '/pages/profile/edit-profile'
  })
}

// 处理菜单点击
const handleMenuClick = (item) => {
  switch (item.key) {
    case 'myTasks':
      uni.switchTab({
        url: '/pages/task/task-list'
      })
      break
    case 'messages':
      uni.navigateTo({
        url: '/pages/messages/messages'
      })
      break
    case 'notifications':
      uni.navigateTo({
        url: '/pages/notifications/notifications'
      })
      break
    case 'announcements':
      uni.navigateTo({
        url: '/pages/announcements/announcements'
      })
      break
    case 'manuals':
      uni.navigateTo({
        url: '/pages/knowledge/manuals'
      })
      break
    case 'help':
      uni.navigateTo({
        url: '/pages/help/help'
      })
      break
    case 'about':
      uni.navigateTo({
        url: '/pages/about/about'
      })
      break
    default:
      showToast('功能开发中...')
  }
}

// 退出登录
const handleLogout = async () => {
  const confirmed = await showConfirm('确认退出登录？')
  if (confirmed) {
    store.logout()
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.profile-header {
  background: linear-gradient(135deg, $uni-color-primary 0%, $uni-color-primary-dark 100%);
  color: white;
  padding: $uni-spacing-lg;
  padding-top: calc($uni-spacing-lg + 44px); // 状态栏高度
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-xl;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: $uni-spacing-lg;
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: $uni-font-size-xl;
  font-weight: bold;
  margin-bottom: $uni-spacing-xs;
}

.work-number {
  display: block;
  font-size: $uni-font-size-base;
  opacity: 0.8;
  margin-bottom: $uni-spacing-xs;
}

.role-text {
  display: block;
  font-size: $uni-font-size-sm;
  opacity: 0.7;
}

.edit-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}



.stats-section {
  background: white;
  margin: $uni-spacing-lg;
  border-radius: $uni-border-radius-lg;
  padding: $uni-spacing-lg;
  box-shadow: $shadow-sm;
}

.section-title {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-lg;
}

.stats-grid {
  display: flex;
  gap: $uni-spacing-lg;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: $uni-font-size-2xl;
  font-weight: bold;
  color: $uni-color-primary;
  margin-bottom: $uni-spacing-xs;
}

.stat-label {
  display: block;
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.menu-section {
  padding: 0 $uni-spacing-lg;
}

.menu-group {
  margin-bottom: $uni-spacing-lg;
}

.group-title {
  font-size: $uni-font-size-base;
  color: $uni-text-color-secondary;
  margin-bottom: $uni-spacing-base;
  padding-left: $uni-spacing-base;
}

.menu-list {
  background: white;
  border-radius: $uni-border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f9fa;
  }
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  margin-right: $uni-spacing-base;
}

.menu-label {
  flex: 1;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: $uni-spacing-sm;
}

.badge {
  background: #F44336;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.logout-section {
  padding: $uni-spacing-lg;
}

.logout-btn {
  width: 100%;
  height: $button-height-lg;
  line-height: $button-height-lg;
  background: #F44336;
  color: white;
  border: none;
  border-radius: $uni-border-radius-lg;
  font-size: $uni-font-size-lg;
  font-weight: 500;
}
</style>
