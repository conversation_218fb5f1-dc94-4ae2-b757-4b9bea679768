<template>
  <view class="container">
    <!-- 用户基本信息 -->
    <view class="user-info-card">
      <view class="user-avatar">
        <text class="avatar-text">{{ user.name ? user.name.charAt(0) : 'U' }}</text>
      </view>
      <view class="user-basic">
        <text class="user-name">{{ user.name }}</text>
        <view class="user-tags">
          <text class="gas-type-tag" :class="'type-' + user.gasType">{{ getGasTypeText(user.gasType) }}</text>
          <text class="service-status" :class="user.isServed ? 'served' : 'unserved'">
            {{ user.isServed ? '已服务' : '未服务' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="info-section">
      <view class="section-title">
        <uni-icons type="phone" size="18" color="#4c6ef5"></uni-icons>
        <text class="title-text">联系信息</text>
      </view>
      <view class="info-card">
        <view class="info-item" @click="callUser">
          <view class="info-label">
            <uni-icons type="phone" size="16" color="#666"></uni-icons>
            <text class="label-text">联系电话</text>
          </view>
          <text class="info-value">{{ user.phone }}</text>
          <uni-icons type="right" size="14" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 地址信息 -->
    <view class="info-section">
      <view class="section-title">
        <uni-icons type="location" size="18" color="#4c6ef5"></uni-icons>
        <text class="title-text">地址信息</text>
      </view>
      <view class="info-card">
        <view class="info-item" @click="openMap">
          <view class="info-label">
            <uni-icons type="location" size="16" color="#666"></uni-icons>
            <text class="label-text">详细地址</text>
          </view>
          <text class="info-value">{{ user.address }}</text>
          <uni-icons type="right" size="14" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 服务信息 -->
    <view class="info-section">
      <view class="section-title">
        <uni-icons type="gear" size="18" color="#4c6ef5"></uni-icons>
        <text class="title-text">服务信息</text>
      </view>
      <view class="info-card">
        <view class="info-item">
          <view class="info-label">
            <uni-icons type="shop" size="16" color="#666"></uni-icons>
            <text class="label-text">服务商</text>
          </view>
          <text class="info-value">{{ user.serviceProvider }}</text>
        </view>
        <view class="info-item" v-if="user.lastServiceDate">
          <view class="info-label">
            <uni-icons type="calendar" size="16" color="#666"></uni-icons>
            <text class="label-text">上次服务</text>
          </view>
          <text class="info-value">{{ formatDate(user.lastServiceDate) }}</text>
        </view>
        <view class="info-item" v-else>
          <view class="info-label">
            <uni-icons type="calendar" size="16" color="#666"></uni-icons>
            <text class="label-text">服务状态</text>
          </view>
          <text class="info-value unserved">尚未服务</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <!-- <button class="action-btn primary" @click="createTask">
        <uni-icons type="plus" size="18" color="white"></uni-icons>
        <text>创建任务</text>
      </button> -->
      <button class="action-btn secondary" @click="callUser">
        <uni-icons type="phone" size="18" color="#4c6ef5"></uni-icons>
        <text>拨打电话</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 响应式数据
const userId = ref('')
const user = reactive({})

// 页面加载时执行
onLoad((options) => {
  userId.value = options.id
  loadUserDetail()
})

// 加载用户详情
const loadUserDetail = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟数据，实际应该根据userId从API获取
    const users = [
      {
        id: 1,
        name: '张先生',
        gasType: 'residential',
        address: '朝阳区建国路88号华贸中心1-2-301',
        phone: '13812341234',
        serviceProvider: '北京燃气配送有限公司',
        isServed: true,
        lastServiceDate: new Date('2024-12-10')
      },
      {
        id: 2,
        name: '李女士',
        gasType: 'commercial',
        address: '海淀区中关村大街1号中关村广场B座',
        phone: '13956785678',
        serviceProvider: '海淀区燃气服务中心',
        isServed: false,
        lastServiceDate: null
      },
      {
        id: 3,
        name: '王总',
        gasType: 'commercial',
        address: '丰台区南三环西路16号搜宝商务中心',
        phone: '13690129012',
        serviceProvider: '丰台燃气工程公司',
        isServed: true,
        lastServiceDate: new Date('2024-12-08')
      },
      {
        id: 4,
        name: '赵女士',
        gasType: 'residential',
        address: '西城区西单北大街133号西单商场',
        phone: '13734563456',
        serviceProvider: '西城燃气服务站',
        isServed: false,
        lastServiceDate: null
      },
      {
        id: 5,
        name: '陈先生',
        gasType: 'mobile',
        address: '东城区王府井大街255号王府井百货',
        phone: '13578907890',
        serviceProvider: '东城区燃气配送中心',
        isServed: true,
        lastServiceDate: new Date('2024-12-05')
      }
    ]

    const foundUser = users.find(u => u.id == userId.value) || {}
    Object.assign(user, foundUser)
  } catch (error) {
    console.error('加载用户详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  }
}

// 获取用气类型文本
const getGasTypeText = (type) => {
  const typeMap = {
    'residential': '居民用户',
    'mobile': '移动用户',
    'commercial': '店铺/企业用户'
  }
  return typeMap[type] || '未知'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${year}年${month}月${day}日`
}

// 拨打电话
const callUser = () => {
  if (!user.phone) return
  uni.makePhoneCall({
    phoneNumber: user.phone
  })
}

// 打开地图
const openMap = () => {
  if (!user.address) return
  uni.openLocation({
    name: user.name,
    address: user.address,
    latitude: 39.9042, // 实际应该根据地址获取坐标
    longitude: 116.4074
  })
}

// 创建任务
const createTask = () => {
  uni.navigateTo({
    url: `/pages/task/create-task?userId=${userId.value}`
  })
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 32rpx;
}

// 用户信息卡片
.user-info-card {
  background: white;
  padding: 48rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4c6ef5 0%, #667eea 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
}

.user-basic {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.user-tags {
  display: flex;
  gap: 12rpx;
}

.gas-type-tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;

  &.type-residential {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.type-mobile {
    background: #f9f0ff;
    color: #722ed1;
  }

  &.type-commercial {
    background: #f6ffed;
    color: #52c41a;
  }
}

.service-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  
  &.served {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.unserved {
    background: #fff1f0;
    color: #ff4d4f;
  }
}

// 信息区域
.info-section {
  margin: 16rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.info-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-width: 160rpx;
}

.label-text {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  margin-right: 16rpx;
  
  &.unserved {
    color: #ff4d4f;
  }
}

// 操作区域
.action-section {
  display: flex;
  gap: 16rpx;
  padding: 32rpx 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  border: none;
  
  &.primary {
    background: #4c6ef5;
    color: white;
  }
  
  &.secondary {
    background: white;
    color: #4c6ef5;
    border: 2rpx solid #4c6ef5;
  }
}
</style>
