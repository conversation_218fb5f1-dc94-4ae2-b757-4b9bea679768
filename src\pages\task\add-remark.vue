<template>
  <view class="page-container">
    <!-- 内容区域 -->
    <scroll-view class="content-section" scroll-y>
      <!-- 备注类型选择 -->
      <view class="form-card">
        <view class="card-header">
          <view class="header-left">
            <view class="header-icon type-icon">
              <uni-icons type="compose" size="20" color="#fff"></uni-icons>
            </view>
            <view class="header-content">
              <text class="section-title">备注类型</text>
              <text class="section-subtitle">选择合适的备注分类</text>
            </view>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <picker
            :range="remarkTypes"
            :value="remarkTypeIndex"
            @change="onRemarkTypeChange"
          >
            <view class="picker-input" :class="{ error: errors.type }">
              <text :class="['picker-text', { placeholder: !remarkForm.type }]">
                {{ remarkForm.type || '请选择备注类型' }}
              </text>
              <text class="picker-arrow">›</text>
            </view>
          </picker>
          <view v-if="errors.type" class="error-message">
            <text class="error-text">⚠️ 请选择备注类型</text>
          </view>
        </view>
      </view>

      <!-- 备注内容 -->
      <view class="form-card">
        <view class="card-header">
          <view class="header-left">
            <view class="header-icon content-icon">
              <uni-icons type="chatbubble" size="20" color="#fff"></uni-icons>
            </view>
            <view class="header-content">
              <text class="section-title">备注内容</text>
              <text class="section-subtitle">详细描述相关信息</text>
            </view>
          </view>
          <text class="required-mark">*</text>
        </view>

        <view class="card-body">
          <textarea
            class="textarea-input"
            :class="{ error: errors.content }"
            placeholder="请详细描述备注内容，如工作进展、问题记录、客户沟通等..."
            v-model="remarkForm.content"
            :maxlength="500"
            show-confirm-bar
            auto-height
            @input="clearContentError"
          />
          <view class="textarea-footer">
            <view v-if="errors.content" class="error-message">
              <text class="error-text">⚠️ 请输入备注内容</text>
            </view>
            <view class="char-count">{{ remarkForm.content.length }}/500</view>
          </view>
        </view>
      </view>

      <!-- 图片上传 -->
      <view class="form-card">
        <view class="card-header">
          <view class="header-left">
            <view class="header-icon image-icon">
              <uni-icons type="image" size="20" color="#fff"></uni-icons>
            </view>
            <view class="header-content">
              <text class="section-title">相关图片</text>
              <text class="section-subtitle">上传相关的图片资料</text>
            </view>
          </view>
        </view>

        <view class="card-body">
          <ImageUpload
            v-model:images="remarkForm.images"
            :max-count="6"
            add-text="添加图片"
            tip-text="最多可上传6张图片，支持JPG、PNG格式"
            :card="false"
            @change="onImagesChange"
          />
        </view>
      </view>



      <!-- 底部安全区域 -->
      <view class="safe-bottom"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <button
          class="main-button submit-btn"
          :disabled="!canSubmit"
          @click="submitRemark"
        >
          <text class="button-text">提交备注</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { taskApi } from '@/api/index.js'
import { showToast, showConfirm } from '../../utils/index'
import ImageUpload from '@/components/ImageUpload.vue'

// 响应式数据
const taskId = ref('')
const taskDetail = ref({})

// 备注表单数据
const remarkForm = ref({
  type: '',
  content: '',
  images: [],
  time: new Date()
})

// 备注类型选项
const remarkTypes = ['工作进展', '问题记录', '客户沟通', '设备状态', '安全提醒', '其他']
const remarkTypeIndex = ref(0)

// 错误状态
const errors = ref({
  type: false,
  content: false
})



// 计算属性
const canSubmit = computed(() => {
  return remarkForm.value.type && remarkForm.value.content.trim()
})

// 页面加载
onLoad((options) => {
  if (options.taskId) {
    taskId.value = options.taskId
    loadTaskDetail()
  }
})



// 加载任务详情
const loadTaskDetail = async () => {
  try {
    const response = await taskApi.getTaskDetail(taskId.value)
    taskDetail.value = response.data
  } catch (error) {
    console.error('加载任务详情失败:', error)
    showToast('加载任务详情失败')
  }
}

// 备注类型选择
const onRemarkTypeChange = (e) => {
  remarkTypeIndex.value = e.detail.value
  remarkForm.value.type = remarkTypes[e.detail.value]
  // 清除类型错误
  errors.value.type = false
}

// 清除内容错误
const clearContentError = () => {
  errors.value.content = false
}

// 验证表单
const validateForm = () => {
  let isValid = true

  // 重置错误状态
  errors.value.type = false
  errors.value.content = false

  // 验证备注类型
  if (!remarkForm.value.type) {
    errors.value.type = true
    isValid = false
    console.log('备注类型验证失败')
  }

  // 验证备注内容
  if (!remarkForm.value.content.trim()) {
    errors.value.content = true
    isValid = false
    console.log('备注内容验证失败')
  }

  console.log('表单验证结果:', isValid, '错误状态:', errors.value)
  return isValid
}

// 图片变化处理
const onImagesChange = (images) => {
  remarkForm.value.images = images
}



// 提交备注
const submitRemark = async () => {
  console.log('开始提交备注，当前表单数据:', remarkForm.value)

  // 验证表单
  if (!validateForm()) {
    console.log('表单验证失败，显示错误提示')
    showToast('请完善必填信息')
    return
  }

  console.log('表单验证通过')
  const confirmed = await showConfirm('确认提交备注？')
  if (!confirmed) return

  try {
    await taskApi.addRemark(taskId.value, remarkForm.value)
    showToast('备注提交成功')

    // 返回上一页
    uni.navigateBack()
  } catch (error) {
    console.error('提交备注失败:', error)
    showToast('提交备注失败')
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  width: 100vw;
  overflow-x: hidden;
}



/* 内容区域 */
.content-section {
  flex: 1;
  padding: 32rpx 24rpx 140rpx;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 表单卡片 */
.form-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.header-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;

  &.type-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
  &.content-icon { background: linear-gradient(135deg, #f093fb, #f5576c); }
  &.image-icon { background: linear-gradient(135deg, #4facfe, #00f2fe); }
  &.time-icon { background: linear-gradient(135deg, #43e97b, #38f9d7); }
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #8c8c8c;
  line-height: 1.2;
}

.card-body {
  padding: 32rpx;
}

.required-mark {
  color: #ff4d4f;
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 8rpx;
}

/* 选择器输入 */
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;

  &:active {
    border-color: #4c6ef5;
    background: #fff;
  }

  &.error {
    border-color: #ff4d4f;
    background: #fff2f0;
  }
}

.picker-text {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;

  &.placeholder {
    color: #999;
    font-weight: 400;
  }
}

.picker-arrow {
  font-size: 32rpx;
  color: #4c6ef5;
  font-weight: bold;
  transform: rotate(0deg);
  transition: transform 0.2s ease;
}

/* 文本域输入 */
.textarea-input {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  font-size: 28rpx;
  color: #262626;
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.2s ease;

  &:focus {
    border-color: #4c6ef5;
    background: #fff;
  }

  &.error {
    border-color: #ff4d4f;
    background: #fff2f0;
  }
}

.textarea-footer {
  display: flex;
  flex-direction: column;
  margin-top: 12rpx;
  gap: 8rpx;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 错误提示 */
.error-message {
  margin-top: 12rpx;
  padding: 12rpx 16rpx;
  background: #fff2f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4d4f;
}

.error-text {
  font-size: 26rpx;
  color: #ff4d4f;
  font-weight: 500;
  line-height: 1.4;
}





/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, #fff 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(240, 240, 240, 0.8);
  padding: 32rpx 24rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.12);
  z-index: 100;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.main-button {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: scale(0.96);

    &::before {
      opacity: 1;
    }
  }

  &:disabled {
    opacity: 0.6;
    transform: none !important;
    background: linear-gradient(135deg, #d9d9d9, #bfbfbf);
    box-shadow: none;

    &::before {
      display: none;
    }
  }
}

.button-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
}

.safe-bottom {
  height: 32rpx;
}
</style>
