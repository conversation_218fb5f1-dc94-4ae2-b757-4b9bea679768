// API 基础配置 - 使用模拟数据
const BASE_URL = 'https://api.gas-mgmt.com' // 替换为实际的API地址

// 模拟延迟函数
const mockDelay = (ms = 1000) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟数据生成器
const mockData = {
  // 生成任务列表
  generateTasks(count = 10) {
    const tasks = []
    const types = ['maintenance', 'inspection', 'delivery', 'repair']
    const statuses = ['new', 'accepted', 'progress', 'completed']
    const priorities = ['low', 'normal', 'high', 'urgent']

    for (let i = 1; i <= count; i++) {
      const createTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
      tasks.push({
        id: i,
        taskNumber: `GS${String(i).padStart(6, '0')}`,
        title: `燃气设备${types[Math.floor(Math.random() * types.length)]}任务`,
        description: `需要对燃气设备进行${types[Math.floor(Math.random() * types.length)]}操作，确保设备正常运行。`,
        type: types[Math.floor(Math.random() * types.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        address: `北京市朝阳区建国路${i}号`,
        customerName: `客户${i}`,
        customerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
        deviceModel: `GM-${Math.floor(Math.random() * 1000)}`,
        deviceSN: `SN${String(Math.floor(Math.random() * 1000000)).padStart(8, '0')}`,
        createTime,
        planTime: new Date(createTime.getTime() + 24 * 60 * 60 * 1000),
        estimatedDuration: Math.floor(Math.random() * 4 + 1) + '小时',
        assignedTo: '张三',
        latitude: 39.9042 + (Math.random() - 0.5) * 0.1,
        longitude: 116.4074 + (Math.random() - 0.5) * 0.1
      })
    }
    return tasks
  },

  // 生成消息列表
  generateMessages(count = 20) {
    const messages = []
    const types = ['task', 'system', 'notice', 'warning']

    for (let i = 1; i <= count; i++) {
      messages.push({
        id: i,
        title: `消息标题${i}`,
        content: `这是第${i}条消息的详细内容，包含重要的工作信息。`,
        type: types[Math.floor(Math.random() * types.length)],
        isRead: Math.random() > 0.3,
        createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        sender: '系统管理员'
      })
    }
    return messages.sort((a, b) => b.createTime - a.createTime)
  },

  // 生成知识库文档
  generateDocuments(count = 30) {
    const documents = []
    const categories = ['installation', 'maintenance', 'repair', 'inspection', 'safety', 'troubleshooting']
    const types = ['manual', 'guide', 'faq', 'video', 'document']

    for (let i = 1; i <= count; i++) {
      const category = categories[Math.floor(Math.random() * categories.length)]
      documents.push({
        id: i,
        title: `${category}相关文档${i}`,
        description: `这是关于${category}的详细说明文档，包含操作步骤和注意事项。`,
        content: `详细内容${i}...`,
        category,
        categoryName: this.getCategoryName(category),
        type: types[Math.floor(Math.random() * types.length)],
        views: Math.floor(Math.random() * 1000),
        isFavorite: Math.random() > 0.7,
        updateTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        author: '技术专家',
        cover: `/static/images/doc-cover-${i % 5 + 1}.jpg`
      })
    }
    return documents.sort((a, b) => b.updateTime - a.updateTime)
  },

  getCategoryName(category) {
    const map = {
      installation: '安装',
      maintenance: '维护',
      repair: '维修',
      inspection: '巡检',
      safety: '安全',
      troubleshooting: '故障排除'
    }
    return map[category] || category
  }
}

// 请求拦截器（模拟版本）
const request = (options) => {
  console.log('API请求:', options)
  return mockDelay(500).then(() => {
    // 这里可以根据URL返回不同的模拟数据
    return { success: true, data: null }
  })
}

// 用户相关API
export const userApi = {
  // 发送验证码
  sendSms(phone) {
    return mockDelay(1000).then(() => {
      console.log('发送验证码到:', phone)
      return { success: true, message: '验证码已发送' }
    })
  },

  // 登录
  login(data) {
    return mockDelay(1000).then(() => {
      if (data.code === '123456') {
        const userInfo = {
          id: 1,
          name: '张三',
          phone: data.phone,
          role: data.role,
          avatar: '/static/images/avatar.jpg',
          department: '维保部门',
          workNumber: 'GS001',
          joinDate: '2023-01-15',
          totalTasks: 156,
          completedTasks: 142,
          rating: 4.8
        }
        const token = 'mock_token_' + Date.now()

        // 保存到本地存储
        uni.setStorageSync('token', token)
        uni.setStorageSync('userInfo', userInfo)

        return { token, userInfo }
      } else {
        throw new Error('验证码错误')
      }
    })
  },

  // 获取用户信息
  getUserInfo() {
    return mockDelay(500).then(() => {
      return {
        id: 1,
        name: '张三',
        phone: '13800138000',
        role: 'maintenance',
        avatar: '/static/images/avatar.jpg',
        department: '维保部门',
        workNumber: 'GS001',
        joinDate: '2023-01-15',
        totalTasks: 156,
        completedTasks: 142,
        rating: 4.8,
        isCheckedIn: false,
        lastCheckIn: null,
        monthlyStats: {
          tasksCompleted: 28,
          workHours: 186,
          rating: 4.8,
          onTimeRate: 95
        }
      }
    })
  },

  // 更新用户信息
  updateUserInfo(data) {
    return mockDelay(1000).then(() => {
      return { success: true, message: '更新成功' }
    })
  },

  // 签到
  checkIn(location) {
    return mockDelay(1000).then(() => {
      return {
        success: true,
        checkTime: new Date(),
        location: location || '北京市朝阳区建国路88号',
        message: '签到成功'
      }
    })
  },

  // 签退
  checkOut(location) {
    return mockDelay(1000).then(() => {
      return {
        success: true,
        checkTime: new Date(),
        location: location || '北京市朝阳区建国路88号',
        workDuration: '8小时30分钟',
        message: '签退成功'
      }
    })
  },

  // 获取签到记录
  getCheckRecords(params) {
    return mockDelay(500).then(() => {
      const records = []
      for (let i = 0; i < 10; i++) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        records.push({
          id: i + 1,
          date: date,
          checkInTime: new Date(date.getTime() + 8 * 60 * 60 * 1000),
          checkOutTime: i < 5 ? new Date(date.getTime() + 17 * 60 * 60 * 1000) : null,
          workDuration: i < 5 ? '9小时' : null,
          location: '北京市朝阳区建国路88号'
        })
      }
      return { list: records, total: records.length }
    })
  }
}

// 模拟任务状态存储
const taskStatusStorage = {}

// 任务相关API
export const taskApi = {
  // 获取任务列表
  getTaskList(params = {}) {
    return mockDelay(800).then(() => {
      let tasks = mockData.generateTasks(20)

      // 根据参数筛选
      if (params.status) {
        tasks = tasks.filter(task => task.status === params.status)
      }
      if (params.type) {
        tasks = tasks.filter(task => task.type === params.type)
      }
      if (params.priority) {
        tasks = tasks.filter(task => task.priority === params.priority)
      }
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        tasks = tasks.filter(task =>
          task.title.toLowerCase().includes(keyword) ||
          task.description.toLowerCase().includes(keyword) ||
          task.taskNumber.toLowerCase().includes(keyword)
        )
      }

      // 分页
      const page = params.page || 1
      const limit = params.limit || 10
      const start = (page - 1) * limit
      const end = start + limit

      return {
        list: tasks.slice(start, end),
        total: tasks.length,
        page,
        limit
      }
    })
  },

  // 获取任务详情
  getTaskDetail(id) {
    return mockDelay(500).then(() => {
      const types = ['install', 'repair', 'rectification']
      const statuses = ['new', 'accepted', 'arrived', 'safety_checked', 'progress', 'completed']
      const typeIndex = (parseInt(id) - 1) % types.length
      const statusIndex = (parseInt(id) - 1) % statuses.length

      // 使用存储的状态，如果没有则使用默认状态
      const currentStatus = taskStatusStorage[id] || statuses[statusIndex]

      const task = {
        id: parseInt(id),
        taskNumber: `GAS${String(id).padStart(6, '0')}`,
        title: `燃气设备${types[typeIndex] === 'install' ? '安装' : types[typeIndex] === 'repair' ? '维修' : '整改'}任务`,
        customerName: `客户${id}`,
        customerPhone: `138${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
        address: `北京市朝阳区某某街道${id}号`,
        detailAddress: `${id}号楼${Math.floor(Math.random() * 6) + 1}单元${Math.floor(Math.random() * 20) + 1}01室`,
        deviceModel: 'GAS-2024-A1',
        serialNumber: `SN${String(Math.floor(Math.random() * 1000000)).padStart(8, '0')}`,
        techSpecs: '额定压力: 2.5MPa, 流量范围: 0.04-25m³/h',
        status: currentStatus,
        type: types[typeIndex],
        appointmentTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        requirements: '请按照标准流程进行设备检查和维护，确保设备正常运行。注意安全操作规范。',
        remarks: '客户要求在工作日上午进行，需要提前电话联系。',
        createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        acceptTime: currentStatus !== 'new' ? new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString() : null,
        arriveTime: ['arrived', 'safety_checked', 'safety_failed', 'progress', 'materials_uploaded', 'info_filled', 'under_review', 'completed'].includes(currentStatus) ? new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString() : null,
        safetyCheckTime: ['safety_checked', 'progress', 'materials_uploaded', 'info_filled', 'under_review', 'completed'].includes(currentStatus) ? new Date(Date.now() - 4.5 * 60 * 60 * 1000).toISOString() : null,
        startTime: currentStatus === 'progress' || currentStatus === 'materials_uploaded' || currentStatus === 'info_filled' || currentStatus === 'under_review' || currentStatus === 'completed' ? new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString() : null,
        completeTime: currentStatus === 'completed' ? new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() : null,
        latitude: 39.908823 + (Math.random() - 0.5) * 0.1,
        longitude: 116.397470 + (Math.random() - 0.5) * 0.1
      }

      // 添加备件信息
      task.parts = [
        { id: 1, name: '燃气表', specification: 'GM-2024-A1', quantity: 1, unit: '台' },
        { id: 2, name: '密封圈', specification: 'SR-20mm', quantity: 2, unit: '个' },
        { id: 3, name: '连接管', specification: 'CP-15mm', quantity: 1, unit: '根' }
      ]

      // 添加工作记录
      task.workRecords = [
        {
          id: 1,
          type: '现场检查',
          content: '设备外观检查正常，无明显损坏，周围环境安全',
          time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          images: []
        },
        {
          id: 2,
          type: '参数测量',
          content: '设备运行参数正常，压力2.3MPa，流量稳定',
          time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          images: ['/static/images/work-1.jpg', '/static/images/work-2.jpg']
        }
      ]

      return task
    })
  },

  // 接收任务
  acceptTask(id) {
    return mockDelay(1000).then(() => {
      taskStatusStorage[id] = 'accepted'
      return { success: true, message: '任务已接收' }
    })
  },

  // 到达现场
  arriveAtSite(id) {
    return mockDelay(1000).then(() => {
      taskStatusStorage[id] = 'arrived'
      return { success: true, message: '已确认到达现场' }
    })
  },

  // 安全环境检查确认
  confirmSafetyCheck(id, isPass) {
    return mockDelay(1000).then(() => {
      if (isPass) {
        taskStatusStorage[id] = 'safety_checked'
        return { success: true, message: '安全环境检查通过' }
      } else {
        // 如果环境不合格，可以设置为特殊状态或保持当前状态
        taskStatusStorage[id] = 'safety_failed'
        return { success: true, message: '安全环境不合格' }
      }
    })
  },

  // 重新检查安全环境
  recheckSafety(id) {
    return mockDelay(1000).then(() => {
      taskStatusStorage[id] = 'arrived'
      return { success: true, message: '已重置为待检查状态' }
    })
  },

  // 开始任务
  startTask(id) {
    return mockDelay(1000).then(() => {
      taskStatusStorage[id] = 'progress'
      return { success: true, message: '任务已开始' }
    })
  },

  // 上传资料
  uploadMaterials(id, materials) {
    return mockDelay(1500).then(() => {
      taskStatusStorage[id] = 'materials_uploaded'
      return { success: true, message: '资料上传成功' }
    })
  },

  // 提交服务信息
  submitServiceInfo(id, serviceInfo) {
    return mockDelay(1500).then(() => {
      taskStatusStorage[id] = 'under_review'
      return { success: true, message: '服务信息提交成功，已进入审核流程' }
    })
  },

  // 完成任务
  completeTask(id, data) {
    return mockDelay(1500).then(() => {
      taskStatusStorage[id] = 'completed'
      return { success: true, message: '任务已完成' }
    })
  },

  // 上传图片
  uploadImage(filePath) {
    return mockDelay(2000).then(() => {
      // 模拟上传成功
      return {
        url: '/static/images/uploaded-' + Date.now() + '.jpg',
        filename: 'image_' + Date.now() + '.jpg'
      }
    })
  },

  // 获取今日统计
  getTodayStats() {
    return mockDelay(500).then(() => {
      return {
        totalTasks: 8,
        completedTasks: 5,
        progressTasks: 2,
        newTasks: 1,
        workHours: 6.5,
        efficiency: 85
      }
    })
  }
}

// 消息相关API
export const messageApi = {
  // 获取消息列表
  getMessages(params = {}) {
    return mockDelay(600).then(() => {
      let messages = mockData.generateMessages(20)

      // 根据类型筛选
      if (params.type) {
        messages = messages.filter(msg => msg.type === params.type)
      }

      // 根据已读状态筛选
      if (params.isRead !== undefined) {
        messages = messages.filter(msg => msg.isRead === params.isRead)
      }

      // 分页
      const page = params.page || 1
      const limit = params.limit || 10
      const start = (page - 1) * limit
      const end = start + limit

      return {
        list: messages.slice(start, end),
        total: messages.length,
        unreadCount: messages.filter(msg => !msg.isRead).length
      }
    })
  },

  // 标记消息已读
  markAsRead(ids) {
    return mockDelay(500).then(() => {
      return { success: true, message: '已标记为已读' }
    })
  },

  // 获取未读消息数量
  getUnreadCount() {
    return mockDelay(300).then(() => {
      return { count: Math.floor(Math.random() * 10) }
    })
  }
}

// 知识库相关API
export const knowledgeApi = {
  // 获取文档列表
  getDocuments(params = {}) {
    return mockDelay(800).then(() => {
      let documents = mockData.generateDocuments(30)

      // 根据分类筛选
      if (params.category && params.category !== 'all') {
        documents = documents.filter(doc => doc.category === params.category)
      }

      // 根据关键词搜索
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        documents = documents.filter(doc =>
          doc.title.toLowerCase().includes(keyword) ||
          doc.description.toLowerCase().includes(keyword) ||
          doc.content.toLowerCase().includes(keyword)
        )
      }

      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 10
      const start = (page - 1) * pageSize
      const end = start + pageSize

      return {
        list: documents.slice(start, end),
        total: documents.length
      }
    })
  },

  // 获取文档详情
  getDocumentDetail(id) {
    return mockDelay(500).then(() => {
      const doc = mockData.generateDocuments(1)[0]
      doc.id = parseInt(id)
      doc.content = `
# ${doc.title}

## 概述
这是一份详细的技术文档，包含了完整的操作步骤和注意事项。

## 操作步骤
1. 准备工作
   - 检查工具是否齐全
   - 确认安全措施到位

2. 具体操作
   - 按照标准流程执行
   - 记录关键参数

3. 验收确认
   - 测试设备功能
   - 确认操作结果

## 注意事项
- 严格按照安全规范操作
- 及时记录操作过程
- 遇到问题及时上报

## 相关资料
- 技术手册 v2.1
- 安全操作规范
- 故障排除指南
      `
      return doc
    })
  },

  // 添加收藏
  addFavorite(id) {
    return mockDelay(500).then(() => {
      return { success: true, message: '已添加到收藏' }
    })
  },

  // 取消收藏
  removeFavorite(id) {
    return mockDelay(500).then(() => {
      return { success: true, message: '已取消收藏' }
    })
  },

  // 搜索知识库
  search(keyword) {
    return mockDelay(800).then(() => {
      const documents = mockData.generateDocuments(30)
      const results = documents.filter(doc =>
        doc.title.toLowerCase().includes(keyword.toLowerCase()) ||
        doc.description.toLowerCase().includes(keyword.toLowerCase())
      ).slice(0, 10)

      return { list: results, total: results.length }
    })
  }
}

// 打卡相关API
export const checkinApi = {
  // 上线打卡
  checkIn(data) {
    return mockDelay(1000).then(() => {
      console.log('上线打卡:', data)
      return {
        success: true,
        message: '上线打卡成功',
        data: {
          checkinTime: new Date().toISOString(),
          location: data.address || '位置信息',
          status: 'online'
        }
      }
    })
  },

  // 下线打卡
  checkOut(data) {
    return mockDelay(1000).then(() => {
      console.log('下线打卡:', data)
      return {
        success: true,
        message: '下线打卡成功',
        data: {
          checkoutTime: new Date().toISOString(),
          location: data.address || '位置信息',
          status: 'offline'
        }
      }
    })
  },

  // 获取打卡状态
  getCheckinStatus() {
    return mockDelay(500).then(() => {
      return {
        success: true,
        data: {
          isOnline: false,
          lastCheckinTime: null,
          todayWorkTime: '0小时0分钟'
        }
      }
    })
  },

  // 获取打卡记录
  getCheckinRecords(params = {}) {
    return mockDelay(800).then(() => {
      const { page = 1, pageSize = 10 } = params
      const records = []

      // 生成模拟打卡记录
      for (let i = 0; i < pageSize; i++) {
        const id = (page - 1) * pageSize + i + 1
        const time = new Date(Date.now() - i * 2 * 60 * 60 * 1000) // 每2小时一条记录
        const type = i % 2 === 0 ? 'checkin' : 'checkout'

        records.push({
          id,
          type,
          time: time.toISOString(),
          location: `北京市朝阳区建国路${88 + i}号`,
          latitude: 39.908823 + (Math.random() - 0.5) * 0.01,
          longitude: 116.397470 + (Math.random() - 0.5) * 0.01
        })
      }

      return {
        success: true,
        data: {
          list: records,
          total: 100,
          page,
          pageSize
        }
      }
    })
  },

  // 获取工作统计
  getWorkStats() {
    return mockDelay(600).then(() => {
      return {
        success: true,
        data: {
          todayTaskCount: 3,
          weekTaskCount: 15,
          monthTaskCount: 68,
          totalWorkTime: '156小时',
          todayWorkTime: '6小时30分钟',
          thisWeekWorkTime: '42小时15分钟',
          thisMonthWorkTime: '186小时45分钟'
        }
      }
    })
  },

  // 获取附近可接任务
  getNearbyTasks(params = {}) {
    return mockDelay(1000).then(() => {
      const { latitude, longitude, radius = 10 } = params
      const tasks = []

      // 生成模拟附近任务
      for (let i = 1; i <= 5; i++) {
        tasks.push({
          id: i,
          taskNumber: `GAS20250100${i}`,
          title: ['燃气表安装', '燃气管道检修', '安全检查', '设备维护', '故障排除'][i - 1],
          address: `北京市朝阳区建国路${80 + i * 10}号`,
          distance: (Math.random() * radius).toFixed(1),
          priority: ['normal', 'high', 'urgent'][Math.floor(Math.random() * 3)],
          estimatedTime: Math.floor(Math.random() * 4 + 1) + '小时',
          customerName: `客户${i}`,
          customerPhone: `138000${1380 + i}`,
          latitude: latitude + (Math.random() - 0.5) * 0.1,
          longitude: longitude + (Math.random() - 0.5) * 0.1,
          createTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
        })
      }

      return {
        success: true,
        data: {
          list: tasks,
          total: tasks.length
        }
      }
    })
  }
}

// 导出默认请求函数和模拟数据
export default request
export { mockData }
