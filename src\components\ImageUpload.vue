<template>
  <view class="upload-container" :class="{ 'card-style': card }">
    <view class="upload-grid">
      <view
        class="upload-item"
        v-for="(image, index) in images"
        :key="index"
        @click="previewImage<PERSON>andler(images, index)"
      >
        <image class="upload-image" :src="image" mode="aspectFill" />
        <view class="delete-btn" @click.stop="deleteImage(index)">
          <text class="delete-icon">×</text>
        </view>
      </view>

      <view
        v-if="images.length < maxCount"
        class="upload-item add-item"
        @click="chooseImage"
      >
        <view class="add-content">
          <text class="add-icon">+</text>
          <text class="add-text">{{ addText }}</text>
        </view>
      </view>
    </view>

    <view v-if="showTip" class="upload-info" :class="{ 'card-info': card }">
      <text class="upload-tip">{{ tipText }}</text>
    </view>
  </view>
</template>

<script setup>
import { previewImage, showToast } from '@/utils/index'

// 定义 props
const props = defineProps({
  // 图片数组
  images: {
    type: Array,
    default: () => []
  },
  // 最大上传数量
  maxCount: {
    type: Number,
    default: 6
  },
  // 添加按钮文字
  addText: {
    type: String,
    default: '添加照片'
  },
  // 提示文字
  tipText: {
    type: String,
    default: '最多可上传6张图片'
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true
  },
  // 是否显示卡片样式
  card: {
    type: Boolean,
    default: false
  },
  // 图片质量
  sizeType: {
    type: Array,
    default: () => ['compressed']
  },
  // 图片来源
  sourceType: {
    type: Array,
    default: () => ['camera', 'album']
  }
})

// 定义 emits
const emit = defineEmits(['update:images', 'change'])

// 选择图片
const chooseImage = () => {
  const remainingCount = props.maxCount - props.images.length
  
  uni.chooseImage({
    count: remainingCount,
    sizeType: props.sizeType,
    sourceType: props.sourceType,
    success: (res) => {
      const newImages = [...props.images, ...res.tempFilePaths]
      emit('update:images', newImages)
      emit('change', newImages)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      showToast('选择图片失败')
    }
  })
}

// 删除图片
const deleteImage = (index) => {
  const newImages = [...props.images]
  newImages.splice(index, 1)
  emit('update:images', newImages)
  emit('change', newImages)
}

// 预览图片
const previewImageHandler = (images, current) => {
  previewImage(images, current)
}
</script>

<style lang="scss" scoped>
.upload-container {
  width: 100%;

  &.card-style {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
    border: 2rpx solid #f0f0f0;
  }
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 16rpx;

  .card-style & {
    gap: 20rpx;
    margin-bottom: 24rpx;
  }
}

.upload-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(255, 77, 79, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.2s ease;
  backdrop-filter: blur(4rpx);
  
  &:active {
    background: rgba(255, 77, 79, 1);
    transform: scale(0.9);
  }
}

.delete-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.add-item {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2rpx dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:active {
    border-color: #4c6ef5;
    background: linear-gradient(135deg, #f0f4ff, #e6f0ff);
    transform: scale(0.95);
  }
}

.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.add-icon {
  font-size: 48rpx;
  color: #4c6ef5;
  font-weight: 300;
}

.add-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.upload-info {
  margin-top: 16rpx;

  &.card-info {
    padding-top: 16rpx;
    border-top: 1rpx solid #f0f0f0;
    margin-top: 0;
  }
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}
</style>
