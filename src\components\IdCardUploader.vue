<template>
  <view class="id-card-uploader">
    <view class="upload-box" @click="handleUpload">
      <image 
        v-if="modelValue && modelValue.length > 0" 
        :src="Array.isArray(modelValue) ? modelValue[0] : modelValue" 
        class="upload-image" 
        mode="aspectFill"
      ></image>
      <text v-if="modelValue && modelValue.length > 0" class="delete-icon" @click.stop="handleDelete">×</text>
      <view v-else class="upload-placeholder">
        <view class="id-card-frame" :class="{'back-frame': isBackSide}">
          <view class="frame-corners">
            <view class="corner top-left"></view>
            <view class="corner top-right"></view>
            <view class="corner bottom-left"></view>
            <view class="corner bottom-right"></view>
          </view>
          <view class="frame-guideline" v-if="isBackSide"></view>
          <!-- <view class="frame-photo-area" v-if="!isBackSide"></view> -->
          <view class="card-type-hint">{{ isBackSide ? '身份证反面' : '身份证正面' }}</view>
          <view class="card-instruction">{{ isBackSide ? '请对齐签名条' : '请对齐人像区域' }}</view>
        </view>
        <text class="upload-icon">+</text>
        <text class="upload-text">{{ placeholder }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// 定义 props
const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: () => ''
  },
  placeholder: {
    type: String,
    default: '点击上传照片'
  },
  isBackSide: {
    type: Boolean,
    default: false
  }
})

// 定义 emits
const emit = defineEmits(['update:modelValue', 'delete'])

// 计算属性
const normalizedImages = computed(() => {
  if (Array.isArray(props.modelValue)) {
    return props.modelValue
  }
  return props.modelValue ? [props.modelValue] : []
})

// 方法
const handleUpload = () => {
  // 打开自定义相机页面
  const type = props.isBackSide ? 'back' : 'front'
  uni.navigateTo({
    url: `/pages/id-card-camera/id-card-camera?type=${type}`,
    events: {
      // 监听拍照完成事件
      photoTaken: (data) => {
        console.log('接收到照片:', data.tempImagePath)
        emit('update:modelValue', data.tempImagePath)
      }
    },
    fail: (err) => {
      console.error('导航到相机页面失败:', err)
      // 如果导航失败，回退到使用系统相机
      useSystemCamera()
    }
  })
}

const useSystemCamera = () => {
  // 备用方案：使用系统相机
  uni.chooseImage({
    count: 1,
    sourceType: ['camera'],
    success: (res) => {
      console.log('系统相机返回图片:', res.tempFilePaths[0])
      emit('update:modelValue', res.tempFilePaths[0])
    }
  })
}

const handleDelete = () => {
  emit('delete', 0)
  emit('update:modelValue', '')
}
</script>

<style scoped>
.upload-box {
  width: 400rpx;
  height: 250rpx;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background-color: #fafafa;
  position: relative;
}

.upload-box:active {
  background-color: #f0f0f0;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.upload-icon {
  font-size: 60rpx;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 2;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  position: relative;
  z-index: 2;
}

.id-card-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 10rpx;
  border: 2rpx dashed var(--primary-color, #4c6ef5);
  border-radius: 8rpx;
  opacity: 0.8;
}

.frame-corners {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.corner {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border-color: var(--primary-color, #4c6ef5);
  border-style: solid;
  border-width: 0;
}

.top-left {
  top: 10rpx;
  left: 10rpx;
  border-top-width: 4rpx;
  border-left-width: 4rpx;
}

.top-right {
  top: 10rpx;
  right: 10rpx;
  border-top-width: 4rpx;
  border-right-width: 4rpx;
}

.bottom-left {
  bottom: 10rpx;
  left: 10rpx;
  border-bottom-width: 4rpx;
  border-left-width: 4rpx;
}

.bottom-right {
  bottom: 10rpx;
  right: 10rpx;
  border-bottom-width: 4rpx;
  border-right-width: 4rpx;
}

.frame-guideline {
  position: absolute;
  top: 30%;
  left: 10%;
  width: 80%;
  height: 24%;
  border-bottom: 3rpx dashed rgba(76, 110, 245, 0.7);
}

.frame-photo-area {
  position: absolute;
  top: 22%;
  right: 12%;
  width: 25%;
  height: 56%;
  border: 3rpx dashed rgba(76, 110, 245, 0.7);
}

.back-frame {
  border-radius: 10rpx;
  background-color: rgba(76, 110, 245, 0.05);
}

.delete-icon {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 5;
}

.card-type-hint {
  position: absolute;
  top: 12rpx;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: var(--primary-color, #4c6ef5);
  font-weight: bold;
}

.card-instruction {
  position: absolute;
  bottom: 12rpx;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 22rpx;
  color: #666;
  padding: 4rpx 0;
}
</style> 