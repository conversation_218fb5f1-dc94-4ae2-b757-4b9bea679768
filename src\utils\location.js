/**
 * 位置权限和定位工具类
 * 专门处理小程序、APP、H5的位置权限和定位功能
 */

class LocationUtils {
  constructor() {
    this.isGettingLocation = false
    this.locationCache = null
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 检查位置权限状态
   * @returns {Promise<Object>} 权限状态对象
   */
  async checkPermissionStatus() {
    // #ifdef MP-WEIXIN
    return new Promise((resolve) => {
      uni.getSetting({
        success: (res) => {
          // 优先检查模糊位置权限，如果没有则检查精确位置权限
          const fuzzyStatus = res.authSetting['scope.userFuzzyLocation']
          const locationStatus = res.authSetting['scope.userLocation']

          // 如果有任一位置权限就认为已授权
          const hasPermission = fuzzyStatus === true || locationStatus === true
          const isDenied = fuzzyStatus === false && locationStatus === false

          resolve({
            granted: hasPermission,
            denied: isDenied,
            notDetermined: !hasPermission && !isDenied,
            platform: 'mp-weixin'
          })
        },
        fail: () => {
          resolve({
            granted: false,
            denied: false,
            notDetermined: true,
            platform: 'mp-weixin'
          })
        }
      })
    })
    // #endif

    // #ifdef APP-PLUS
    return new Promise((resolve) => {
      const result = plus.navigator.checkPermission('LOCATION')
      resolve({
        granted: result === 'granted',
        denied: result === 'denied',
        notDetermined: result === 'unknown',
        platform: 'app-plus'
      })
    })
    // #endif

    // #ifdef H5
    return {
      granted: true, // H5由浏览器处理
      denied: false,
      notDetermined: false,
      platform: 'h5'
    }
    // #endif
  }

  /**
   * 请求位置权限
   * @returns {Promise<boolean>} 是否授权成功
   */
  async requestPermission() {
    // #ifdef MP-WEIXIN
    return new Promise((resolve) => {
      // 优先申请模糊位置权限
      uni.authorize({
        scope: 'scope.userFuzzyLocation',
        success: () => {
          console.log('微信小程序模糊位置权限授权成功')
          resolve(true)
        },
        fail: (err) => {
          console.log('模糊位置权限申请失败，尝试精确位置权限:', err)
          // 如果模糊位置权限申请失败，尝试精确位置权限
          uni.authorize({
            scope: 'scope.userLocation',
            success: () => {
              console.log('微信小程序精确位置权限授权成功')
              resolve(true)
            },
            fail: (err2) => {
              console.error('微信小程序位置权限授权失败:', err2)
              resolve(false)
            }
          })
        }
      })
    })
    // #endif

    // #ifdef APP-PLUS
    return new Promise((resolve) => {
      plus.navigator.requestPermission(['LOCATION'], (result) => {
        if (result.granted && result.granted.length > 0) {
          console.log('APP位置权限授权成功')
          resolve(true)
        } else {
          console.error('APP位置权限授权失败')
          resolve(false)
        }
      })
    })
    // #endif

    // #ifdef H5
    return true
    // #endif
  }

  /**
   * 显示权限设置对话框
   * @returns {Promise<boolean>} 用户是否选择去设置
   */
  async showPermissionDialog() {
    return new Promise((resolve) => {
      uni.showModal({
        title: '位置权限申请',
        content: '打卡功能需要获取您的位置信息，请在设置中开启位置权限',
        confirmText: '去设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.openPermissionSetting()
            resolve(true)
          } else {
            resolve(false)
          }
        }
      })
    })
  }

  /**
   * 打开权限设置页面
   */
  openPermissionSetting() {
    // #ifdef MP-WEIXIN
    uni.openSetting({
      success: (res) => {
        console.log('设置页面返回:', res.authSetting)
        if (res.authSetting['scope.userLocation']) {
          uni.showToast({
            title: '授权成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '需要位置权限才能使用定位功能',
            icon: 'none',
            duration: 3000
          })
        }
      },
      fail: (err) => {
        console.error('打开设置页面失败:', err)
      }
    })
    // #endif

    // #ifdef APP-PLUS
    plus.runtime.openURL('app-settings:')
    // #endif
  }

  /**
   * 获取当前位置
   * @param {Object} options 定位选项
   * @returns {Promise<Object>} 位置信息
   */
  async getCurrentLocation(options = {}) {
    // 防止重复获取
    if (this.isGettingLocation) {
      throw new Error('正在获取位置，请稍候')
    }

    // 检查缓存
    if (this.locationCache &&
        Date.now() - this.locationCache.timestamp < this.cacheTimeout) {
      return this.locationCache
    }

    this.isGettingLocation = true

    try {
      const location = await this._getLocation(options)

      // 如果没有地址信息且需要地址解析，尝试逆地理编码
      if ((!location.address || location.address.includes(',')) && options.geocode) {
        try {
          const addressInfo = await this.reverseGeocode(location.latitude, location.longitude)
          if (addressInfo) {
            location.address = addressInfo
          }
        } catch (error) {
          console.log('逆地理编码失败:', error)
        }
      }

      // 缓存位置信息
      this.locationCache = {
        ...location,
        timestamp: Date.now()
      }

      return location
    } finally {
      this.isGettingLocation = false
    }
  }

  /**
   * 内部获取位置方法
   * @param {Object} options 定位选项
   * @returns {Promise<Object>} 位置信息
   */
  _getLocation(options = {}) {
    const defaultOptions = {
      type: 'gcj02', // 国测局坐标系
      geocode: true, // 返回地址信息
      altitude: false, // 不需要高度信息
      timeout: 15000, // 15秒超时
      ...options
    }

    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      // 微信小程序使用 getFuzzyLocation
      uni.getFuzzyLocation({
        ...defaultOptions,
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude,
            address: this.formatAddress(res),
            speed: res.speed || 0,
            accuracy: res.accuracy || 0,
            altitude: res.altitude || 0,
            timestamp: Date.now(),
            coordType: defaultOptions.type
          }

          console.log('获取位置成功:', location)
          resolve(location)
        },
        fail: (error) => {
          console.error('获取位置失败:', error)

          let errorMessage = '位置获取失败'

          if (error.errMsg) {
            if (error.errMsg.includes('auth deny')) {
              errorMessage = '位置权限被拒绝'
            } else if (error.errMsg.includes('location fail')) {
              errorMessage = '定位服务不可用，请检查GPS是否开启'
            } else if (error.errMsg.includes('timeout')) {
              errorMessage = '定位超时，请重试'
            }
          }

          reject(new Error(errorMessage))
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      // 其他平台使用 getLocation
      uni.getLocation({
        ...defaultOptions,
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude,
            address: this.formatAddress(res),
            speed: res.speed || 0,
            accuracy: res.accuracy || 0,
            altitude: res.altitude || 0,
            timestamp: Date.now(),
            coordType: defaultOptions.type
          }

          console.log('获取位置成功:', location)
          resolve(location)
        },
        fail: (error) => {
          console.error('获取位置失败:', error)

          let errorMessage = '位置获取失败'

          if (error.errMsg) {
            if (error.errMsg.includes('auth deny')) {
              errorMessage = '位置权限被拒绝'
            } else if (error.errMsg.includes('location fail')) {
              errorMessage = '定位服务不可用，请检查GPS是否开启'
            } else if (error.errMsg.includes('timeout')) {
              errorMessage = '定位超时，请重试'
            }
          }

          reject(new Error(errorMessage))
        }
      })
      // #endif
    })
  }

  /**
   * 完整的位置获取流程（包含权限检查和申请）
   * @param {Object} options 定位选项
   * @returns {Promise<Object>} 位置信息
   */
  async getLocationWithPermission(options = {}) {
    try {
      // 1. 检查权限状态
      const permissionStatus = await this.checkPermissionStatus()
      
      if (permissionStatus.denied) {
        // 权限被拒绝，引导用户去设置
        const userChoice = await this.showPermissionDialog()
        if (!userChoice) {
          throw new Error('用户拒绝开启位置权限')
        }
        // 用户选择去设置，但我们无法知道用户是否真的开启了权限
        // 所以这里抛出异常，让用户重新尝试
        throw new Error('请在设置中开启位置权限后重试')
      }
      
      if (permissionStatus.notDetermined) {
        // 未授权，请求权限
        const granted = await this.requestPermission()
        if (!granted) {
          throw new Error('位置权限申请失败')
        }
      }

      // 2. 获取位置
      const location = await this.getCurrentLocation(options)
      return location

    } catch (error) {
      console.error('获取位置失败:', error)
      throw error
    }
  }

  /**
   * 清除位置缓存
   */
  clearCache() {
    this.locationCache = null
  }

  /**
   * 逆地理编码 - 根据坐标获取地址
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @returns {Promise<string>} 地址信息
   */
  async reverseGeocode(latitude, longitude) {
    // 实机运行时不使用模拟地址，直接返回 null
    // 让系统使用原始的位置信息
    console.log('逆地理编码暂未实现，使用原始位置信息', { latitude, longitude })
    return null
  }

  /**
   * 格式化地址信息
   * @param {Object} res 位置响应对象
   * @returns {string} 格式化后的地址
   */
  formatAddress(res) {
    // 如果有详细地址信息，优先使用
    if (res.address && res.address.trim() && !res.address.includes(',')) {
      return res.address.trim()
    }

    // 如果有省市区信息，组合显示
    const addressParts = []
    if (res.province) addressParts.push(res.province)
    if (res.city) addressParts.push(res.city)
    if (res.district) addressParts.push(res.district)
    if (res.street) addressParts.push(res.street)

    if (addressParts.length > 0) {
      return addressParts.join('')
    }

    // 如果都没有，返回坐标
    return `${res.latitude.toFixed(6)}, ${res.longitude.toFixed(6)}`
  }
}

// 创建单例
const locationUtils = new LocationUtils()

export default locationUtils
