<template>
  <view class="checkin-container">
    <!-- 顶部状态栏 -->
    <view class="status-bar">
      <view class="location-info">
        <uni-icons type="location" size="16" color="#666"></uni-icons>
        <text class="location-text">{{ currentLocation || '获取位置中...' }}</text>
        <text v-if="locationAccuracy" class="location-accuracy">{{ locationAccuracy }}m</text>
      </view>
      <view class="status-info">
        <text class="status-text" :class="workStatus">{{ getStatusText() }}</text>
        <text class="work-time">{{ workTimeDisplay }}</text>
      </view>
      <view class="refresh-btn" @click="refreshLocation">
        <uni-icons type="refresh" size="16" color="#999"></uni-icons>
      </view>
    </view>

    <!-- 打卡主区域 -->
    <view class="checkin-main">
      <view class="checkin-card">
        <view class="checkin-time">
          <text class="current-time">{{ currentTime }}</text>
          <text class="current-date">{{ currentDate }}</text>
        </view>

        <view class="checkin-button-area">
          <view
            class="checkin-button"
            :class="{ 'online': isOnline, 'offline': !isOnline }"
            @click="toggleWorkStatus"
          >
            <uni-icons
              :type="isOnline ? 'clock' : 'notification'"
              size="40"
              color="white"
              class="button-icon"
            ></uni-icons>
            <text class="button-text">{{ isOnline ? '下线' : '上线' }}</text>
          </view>
        </view>

        <view class="work-info">
          <view class="info-item">
            <text class="info-label">今日工作时长</text>
            <text class="info-value">{{ todayWorkTime }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">当前状态</text>
            <text class="info-value" :class="workStatus">{{ getStatusText() }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 可接任务列表 -->
    <view v-if="isOnline" class="available-tasks">
      <view class="section-header">
        <view class="section-title-with-icon">
          <uni-icons type="list" size="18" color="#333"></uni-icons>
          <text class="section-title">可接任务</text>
        </view>
        <view class="task-refresh-btn" @click="refreshTasks" :class="{ 'refreshing': isRefreshing }">
          <uni-icons type="refresh" size="16" color="#fff" :class="{ 'rotating': isRefreshing }"></uni-icons>
          <text class="refresh-text">{{ isRefreshing ? '刷新中' : '刷新' }}</text>
        </view>
      </view>

      <view v-if="availableTasks.length > 0" class="task-list">
        <view
          class="task-item"
          v-for="task in availableTasks"
          :key="task.id"
          @click="viewTaskDetail(task)"
        >
          <view class="task-info">
            <view class="task-header">
              <text class="task-number">{{ task.taskNumber }}</text>
              <view class="task-type-tag" :class="'type-' + (task.type || 'maintenance')">
                <text class="type-text">{{ getTaskTypeText(task.type) }}</text>
              </view>
            </view>
            <text class="task-customer">{{ task.customerName || '待分配' }}</text>
            <view class="task-address">
              <view class="address-info">
                <uni-icons type="location" size="14" color="#999"></uni-icons>
                <text class="address-text">{{ task.address }}</text>
              </view>
              <text class="task-distance">{{ task.distance }}km</text>
            </view>
          </view>
          <view class="task-actions">
            <button class="accept-btn" @click.stop="acceptTask(task)">接单</button>
          </view>
        </view>
      </view>

      <view v-else class="empty-tasks">
        <uni-icons type="compose" size="60" color="#ccc" class="empty-icon"></uni-icons>
        <text class="empty-text">暂无可接任务</text>
      </view>
    </view>

    <!-- 当前任务 -->
    <view v-if="currentTask" class="current-task">
      <view class="section-header">
        <view class="section-title-with-icon">
          <uni-icons type="gear" size="18" color="#333"></uni-icons>
          <text class="section-title">当前任务</text>
        </view>
        <view class="task-status" :class="'status-' + currentTask.status">
          <text class="status-text">{{ getTaskStatusText(currentTask.status) }}</text>
        </view>
      </view>

      <view class="task-content">
        <text class="task-number">{{ currentTask.taskNumber }}</text>
        <text class="task-desc">{{ currentTask.title }}</text>
        <view class="task-address">
          <uni-icons type="location" size="14" color="#999"></uni-icons>
          <text class="address-text">{{ currentTask.address }}</text>
        </view>
        <view class="task-actions">
          <button class="task-btn primary" @click="goToTaskDetail">查看详情</button>
          <button class="task-btn secondary" @click="startWork">开始作业</button>
        </view>
      </view>
    </view>

    <!-- 打卡记录 -->
    <view class="checkin-records">
      <view class="section-header">
        <view class="section-title-with-icon">
          <uni-icons type="compose" size="18" color="#333"></uni-icons>
          <text class="section-title" @longpress="clearCheckinRecords">打卡记录</text>
        </view>
        <text class="more-btn" @click="viewAllRecords">查看全部</text>
      </view>

      <view v-if="checkinRecords.length > 0" class="record-list">
        <view
          class="record-item"
          v-for="record in checkinRecords"
          :key="record.id"
        >
          <view class="record-content">
            <text class="record-title">{{ record.type === 'checkin' ? '上线打卡' : '下线打卡' }}</text>
            <text class="record-time">{{ formatTime(record.time) }}</text>
            <view class="record-location">
              <uni-icons type="location" size="12" color="#999"></uni-icons>
              <text class="location-text">{{ record.location }}</text>
            </view>
          </view>
          <view class="record-status">
            <text class="status-dot success"></text>
          </view>
        </view>
      </view>

      <view v-else class="empty-records">
        <text class="empty-text">暂无打卡记录</text>
      </view>
    </view>

    <!-- 工作统计 -->
    <view class="work-stats">
      <view class="section-header">
        <view class="section-title-with-icon">
          <uni-icons type="bars" size="18" color="#333"></uni-icons>
          <text class="section-title">工作统计</text>
        </view>
      </view>

      <!-- 任务统计 -->
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{ todayTaskCount }}</text>
          <text class="stat-label">今日任务</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ weekTaskCount }}</text>
          <text class="stat-label">本周任务</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ monthTaskCount }}</text>
          <text class="stat-label">本月任务</text>
        </view>
      </view>

      <!-- 工作时长统计 -->
      <view class="work-time-stats">
        <view class="time-stat-item">
          <view class="time-stat-content">
            <text class="time-stat-label">今日工作时长</text>
            <text class="time-stat-value">{{ todayWorkTime }}</text>
          </view>
        </view>
        <view class="time-stat-item">
          <view class="time-stat-content">
            <text class="time-stat-label">本月总工时</text>
            <text class="time-stat-value">{{ totalWorkTime }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { onLoad, onShow, onUnload } from '@dcloudio/uni-app'
import { taskApi } from '@/api/index.js'
import { formatRelativeTime, showToast, showLoading, hideLoading } from '@/utils/index.js'
import locationUtils from '@/utils/location.js'

// 响应式数据
const currentLocation = ref('')
const locationAccuracy = ref(null)
const currentTime = ref('')
const currentDate = ref('')
const isOnline = ref(false)
const workStartTime = ref(null)
const workTimeDisplay = ref('00:00:00')
const todayWorkTime = ref('0小时0分钟')
const currentTask = ref(null)
const availableTasks = ref([])
const isRefreshing = ref(false)
const checkinRecords = ref([])

// 工作统计
const todayTaskCount = ref(0)
const weekTaskCount = ref(0)
const monthTaskCount = ref(0)
const totalWorkTime = ref('0小时')

// 定时器
let timeTimer = null
let workTimeTimer = null

// 计算属性
const workStatus = computed(() => {
  return isOnline.value ? 'online' : 'offline'
})

// 生命周期钩子
onLoad(async () => {
  await initPage()

  // 检查位置权限状态
  try {
    const permissionStatus = await locationUtils.checkPermissionStatus()
    console.log('位置权限状态:', permissionStatus)

    if (permissionStatus.denied) {
      currentLocation.value = '位置权限被拒绝，点击打卡按钮重新授权'
    } else if (permissionStatus.notDetermined) {
      currentLocation.value = '点击打卡按钮获取位置权限'
    } else if (permissionStatus.granted) {
      // 如果已授权，尝试获取当前位置
      getCurrentLocation()
    }
  } catch (error) {
    console.error('检查位置权限失败:', error)
    currentLocation.value = '位置权限检查失败'
  }
})

onShow(() => {
  loadCurrentTask()
  loadAvailableTasks()
  loadCheckinRecords()
  loadWorkStats()
})

onUnload(() => {
  clearTimers()
})

// 确保在组件卸载时清理定时器
onUnmounted(() => {
  clearTimers()
})

// 初始化页面
const initPage = async () => {
  await getCurrentLocation()
  updateCurrentTime()
  loadWorkStatus()
  loadCurrentTask()
  loadAvailableTasks()
  loadCheckinRecords()
  loadWorkStats()
  startTimers()
}

// 获取当前位置（用于显示）
const getCurrentLocation = async () => {
  try {
    // 使用位置工具获取带地址的位置信息
    const location = await locationUtils.getLocationWithPermission({
      timeout: 10000,
      geocode: true
    })

    // 优先显示地址，如果没有地址则显示坐标
    currentLocation.value = location.address || `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`
    locationAccuracy.value = Math.round(location.accuracy)
  } catch (error) {
    console.error('获取位置失败:', error)
    currentLocation.value = '位置获取失败'
  }
}

// 获取打卡位置（使用新的位置工具）
const getCurrentLocationForCheckin = async () => {
  try {
    showLoading('正在获取位置...')

    // 使用新的位置工具获取位置
    const location = await locationUtils.getLocationWithPermission({
      timeout: 15000,
      geocode: true
    })

    // 更新显示的位置信息
    currentLocation.value = location.address
    locationAccuracy.value = Math.round(location.accuracy)

    hideLoading()
    return location
  } catch (error) {
    hideLoading()
    console.error('获取打卡位置失败:', error)

    // 显示错误提示
    showToast(error.message || '位置获取失败')
    throw error
  }
}

// 检查位置权限
const checkLocationAuth = async () => {
  return new Promise((resolve) => {
    // #ifdef MP-WEIXIN
    // 微信小程序位置权限检查
    uni.getSetting({
      success: (res) => {
        const fuzzyStatus = res.authSetting['scope.userFuzzyLocation']
        const locationStatus = res.authSetting['scope.userLocation']

        console.log('位置权限状态:', { fuzzyStatus, locationStatus })

        // 如果有任一位置权限就认为已授权
        const hasPermission = fuzzyStatus === true || locationStatus === true
        const isDenied = fuzzyStatus === false && locationStatus === false

        if (isDenied) {
          // 用户拒绝过位置权限，引导用户去设置页面
          showLocationAuthDialog()
          resolve(false)
        } else if (!hasPermission) {
          // 未授权过，直接请求授权
          requestLocationAuth().then(resolve)
        } else {
          // 已授权
          resolve(true)
        }
      },
      fail: (err) => {
        console.error('获取设置失败:', err)
        resolve(false)
      }
    })
    // #endif

    // #ifdef APP-PLUS
    // APP端权限检查
    const result = plus.navigator.checkPermission('LOCATION')
    if (result === 'granted') {
      resolve(true)
    } else {
      requestLocationAuth().then(resolve)
    }
    // #endif

    // #ifdef H5
    // H5端直接返回true，由浏览器处理权限
    resolve(true)
    // #endif
  })
}

// 请求位置权限
const requestLocationAuth = async () => {
  return new Promise((resolve) => {
    // #ifdef MP-WEIXIN
    // 优先申请模糊位置权限
    uni.authorize({
      scope: 'scope.userFuzzyLocation',
      success: () => {
        console.log('模糊位置权限授权成功')
        resolve(true)
      },
      fail: (err) => {
        console.log('模糊位置权限申请失败，尝试精确位置权限:', err)
        // 如果模糊位置权限申请失败，尝试精确位置权限
        uni.authorize({
          scope: 'scope.userLocation',
          success: () => {
            console.log('精确位置权限授权成功')
            resolve(true)
          },
          fail: (err2) => {
            console.error('位置权限授权失败:', err2)
            showLocationAuthDialog()
            resolve(false)
          }
        })
      }
    })
    // #endif

    // #ifdef APP-PLUS
    plus.navigator.requestPermission(['LOCATION'], (result) => {
      if (result.granted && result.granted.length > 0) {
        resolve(true)
      } else {
        showLocationAuthDialog()
        resolve(false)
      }
    })
    // #endif

    // #ifdef H5
    resolve(true)
    // #endif
  })
}

// 显示位置权限授权对话框
const showLocationAuthDialog = () => {
  uni.showModal({
    title: '位置权限申请',
    content: '打卡功能需要获取您的位置信息，请在设置中开启位置权限',
    confirmText: '去设置',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // #ifdef MP-WEIXIN
        uni.openSetting({
          success: (settingRes) => {
            console.log('设置页面返回:', settingRes.authSetting)
            const fuzzyStatus = settingRes.authSetting['scope.userFuzzyLocation']
            const locationStatus = settingRes.authSetting['scope.userLocation']
            const hasPermission = fuzzyStatus === true || locationStatus === true

            if (hasPermission) {
              uni.showToast({
                title: '授权成功',
                icon: 'success'
              })
              // 重新获取位置
              setTimeout(() => {
                getCurrentLocationForCheckin()
              }, 1000)
            } else {
              uni.showToast({
                title: '需要位置权限才能打卡',
                icon: 'none'
              })
            }
          },
          fail: (err) => {
            console.error('打开设置页面失败:', err)
          }
        })
        // #endif

        // #ifdef APP-PLUS
        plus.runtime.openURL('app-settings:')
        // #endif
      }
    }
  })
}

// 手动刷新位置信息
const refreshLocation = async () => {
  try {
    showLoading('正在刷新位置...')

    // 清除缓存，强制重新获取
    locationUtils.clearCache()

    const location = await locationUtils.getLocationWithPermission({
      timeout: 10000,
      geocode: true
    })

    currentLocation.value = location.address
    locationAccuracy.value = Math.round(location.accuracy)

    showToast('位置刷新成功')
    hideLoading()
  } catch (error) {
    hideLoading()
    console.error('刷新位置失败:', error)
    showToast(error.message || '位置刷新失败')
  }
}

// 获取位置信息（带权限处理）
const getLocationWithAuth = async () => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    // 微信小程序使用 getFuzzyLocation
    uni.getFuzzyLocation({
      type: 'gcj02', // 国测局坐标系
      geocode: true, // 返回地址信息
      success: (res) => {
        const location = {
          latitude: res.latitude,
          longitude: res.longitude,
          address: res.address || `${res.latitude}, ${res.longitude}`,
          speed: res.speed || 0,
          accuracy: res.accuracy || 0,
          timestamp: Date.now()
        }

        // 更新显示的位置信息
        currentLocation.value = location.address
        locationAccuracy.value = Math.round(location.accuracy)

        resolve(location)
      },
      fail: (error) => {
        console.error('获取位置失败:', error)

        // 根据错误码给出具体提示
        let errorMessage = '位置获取失败'

        if (error.errMsg) {
          if (error.errMsg.includes('auth deny')) {
            errorMessage = '位置权限被拒绝'
          } else if (error.errMsg.includes('location fail')) {
            errorMessage = '定位服务不可用，请检查GPS是否开启'
          } else if (error.errMsg.includes('timeout')) {
            errorMessage = '定位超时，请重试'
          }
        }

        reject(new Error(errorMessage))
      }
    })
    // #endif

    // #ifndef MP-WEIXIN
    // 其他平台使用 getLocation
    uni.getLocation({
      type: 'gcj02', // 国测局坐标系
      geocode: true, // 返回地址信息
      success: (res) => {
        const location = {
          latitude: res.latitude,
          longitude: res.longitude,
          address: res.address || `${res.latitude}, ${res.longitude}`,
          speed: res.speed || 0,
          accuracy: res.accuracy || 0,
          timestamp: Date.now()
        }

        // 更新显示的位置信息
        currentLocation.value = location.address
        locationAccuracy.value = Math.round(location.accuracy)

        resolve(location)
      },
      fail: (error) => {
        console.error('获取位置失败:', error)

        // 根据错误码给出具体提示
        let errorMessage = '位置获取失败'

        if (error.errMsg) {
          if (error.errMsg.includes('auth deny')) {
            errorMessage = '位置权限被拒绝'
          } else if (error.errMsg.includes('location fail')) {
            errorMessage = '定位服务不可用，请检查GPS是否开启'
          } else if (error.errMsg.includes('timeout')) {
            errorMessage = '定位超时，请重试'
          }
        }

        reject(new Error(errorMessage))
      }
    })
    // #endif
  })
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

// 启动定时器
const startTimers = () => {
  // 更新时间定时器
  timeTimer = setInterval(() => {
    updateCurrentTime()
  }, 1000)

  // 工作时间计算定时器
  workTimeTimer = setInterval(() => {
    updateWorkTime()
  }, 1000)
}

// 清除定时器
const clearTimers = () => {
  if (timeTimer) {
    clearInterval(timeTimer)
    timeTimer = null
  }
  if (workTimeTimer) {
    clearInterval(workTimeTimer)
    workTimeTimer = null
  }
}

// 切换工作状态（上线/下线）
const toggleWorkStatus = async () => {
  try {
    if (isOnline.value) {
      // 下线打卡
      await checkOut()
    } else {
      // 上线打卡
      await checkIn()
    }
  } catch (error) {
    showToast('打卡失败，请重试')
  }
}

// 上线打卡
const checkIn = async () => {
  try {
    // 获取位置信息（内部已处理loading）
    const location = await getCurrentLocationForCheckin()

    if (!location) {
      showToast('位置获取失败，无法打卡')
      return
    }

    // 显示打卡加载
    showLoading('正在打卡...')

    // 这里应该调用真实的API
    // const result = await checkinApi.checkIn({
    //   latitude: location.latitude,
    //   longitude: location.longitude,
    //   address: location.address
    // })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    isOnline.value = true
    workStartTime.value = new Date()

    // 保存状态到本地存储
    uni.setStorageSync('workStatus', {
      isOnline: true,
      startTime: workStartTime.value.toISOString(),
      checkinLocation: location
    })

    // 添加打卡记录
    addCheckinRecord('checkin', location)

    hideLoading()
    showToast('上线打卡成功')
    loadAvailableTasks()
  } catch (error) {
    hideLoading()
    console.error('上线打卡失败:', error)
    showToast(error.message || '上线打卡失败')
  }
}

// 下线打卡
const checkOut = async () => {
  try {
    // 获取位置信息（内部已处理loading）
    const location = await getCurrentLocationForCheckin()

    if (!location) {
      showToast('位置获取失败，无法打卡')
      return
    }

    // 显示打卡加载
    showLoading('正在打卡...')

    // 这里应该调用真实的API
    // const result = await checkinApi.checkOut({
    //   latitude: location.latitude,
    //   longitude: location.longitude,
    //   address: location.address
    // })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    isOnline.value = false
    workStartTime.value = null
    workTimeDisplay.value = '00:00:00'

    // 保存状态到本地存储
    uni.setStorageSync('workStatus', {
      isOnline: false,
      startTime: null,
      checkoutLocation: location
    })

    // 添加打卡记录
    addCheckinRecord('checkout', location)

    hideLoading()
    showToast('下线打卡成功')
    availableTasks.value = []
  } catch (error) {
    hideLoading()
    console.error('下线打卡失败:', error)
    showToast(error.message || '下线打卡失败')
  }
}

// 加载工作状态
const loadWorkStatus = () => {
  const workStatusData = uni.getStorageSync('workStatus')
  if (workStatusData) {
    isOnline.value = workStatusData.isOnline
    if (workStatusData.startTime) {
      workStartTime.value = new Date(workStatusData.startTime)
    }
  }
}

// 更新工作时间显示
const updateWorkTime = () => {
  if (isOnline.value && workStartTime.value) {
    const now = new Date()
    const diff = now - workStartTime.value
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    workTimeDisplay.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
}

// 添加打卡记录
const addCheckinRecord = (type, locationInfo = null) => {
  const record = {
    id: Date.now(),
    type: type,
    time: new Date(),
    location: locationInfo ? locationInfo.address : currentLocation.value,
    latitude: locationInfo ? locationInfo.latitude : null,
    longitude: locationInfo ? locationInfo.longitude : null,
    accuracy: locationInfo ? locationInfo.accuracy : null
  }

  checkinRecords.value.unshift(record)

  // 只保留最近10条记录
  if (checkinRecords.value.length > 10) {
    checkinRecords.value = checkinRecords.value.slice(0, 10)
  }

  // 保存到本地存储
  uni.setStorageSync('checkinRecords', checkinRecords.value)
}

// 加载当前任务
const loadCurrentTask = async () => {
  try {
    const tasks = await taskApi.getTaskList({ status: 'progress', limit: 1 })
    currentTask.value = tasks.length > 0 ? tasks[0] : null
  } catch (error) {
    console.error('加载当前任务失败:', error)
  }
}

// 加载可接任务
const loadAvailableTasks = async () => {
  if (!isOnline.value) {
    availableTasks.value = []
    return
  }

  try {
    // 这里应该调用真实的API获取附近的可接任务
    // const tasks = await taskApi.getNearbyTasks({
    //   latitude: location.latitude,
    //   longitude: location.longitude,
    //   radius: 10 // 10公里范围内
    // })

    // 模拟数据
    availableTasks.value = [
      {
        id: 1,
        taskNumber: 'GAS202501001',
        type: 'install',
        address: '朝阳区建国路88号',
        distance: 2.5,
        priority: 'normal',
        customerName: '张先生'
      },
      {
        id: 2,
        taskNumber: 'GAS202501002',
        type: 'repair',
        address: '朝阳区国贸大厦',
        distance: 3.2,
        priority: 'high',
        customerName: '李女士'
      },
      {
        id: 3,
        taskNumber: 'GAS202501003',
        type: 'rectification',
        address: '海淀区中关村大街1号',
        distance: 4.1,
        priority: 'normal',
        customerName: '王先生'
      }
    ]
  } catch (error) {
    console.error('加载可接任务失败:', error)
  }
}

// 加载打卡记录
const loadCheckinRecords = () => {
  const records = uni.getStorageSync('checkinRecords')
  if (records && Array.isArray(records) && records.length > 0) {
    checkinRecords.value = records.map(record => ({
      ...record,
      time: new Date(record.time)
    }))
  } else {
    // 实机运行时不生成模拟数据，保持空列表
    checkinRecords.value = []
    console.log('没有打卡记录，等待用户实际打卡')
  }
}

// 生成模拟打卡记录
const generateMockCheckinRecords = () => {
  const mockRecords = []
  const now = new Date()

  // 生成最近10天的打卡记录（跳过周末）
  let recordCount = 0
  for (let i = 0; i < 20 && recordCount < 10; i++) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)

    // 跳过周末
    if (date.getDay() === 0 || date.getDay() === 6) {
      continue
    }

    recordCount++

    // 上线打卡 (早上8:00-9:30)
    const checkinHour = 8 + Math.floor(Math.random() * 2)
    const checkinMinute = Math.floor(Math.random() * 60)
    const checkinTime = new Date(date)
    checkinTime.setHours(checkinHour, checkinMinute, Math.floor(Math.random() * 60), 0)

    const checkinLocation = getRandomLocation()
    // 这个方法现在不会被调用，但保留代码结构
    mockRecords.push({
      id: Date.now() + i * 100 + 1,
      type: 'checkin',
      time: checkinTime,
      location: checkinLocation,
      latitude: 39.9042 + (Math.random() - 0.5) * 0.02,
      longitude: 116.4074 + (Math.random() - 0.5) * 0.02,
      accuracy: Math.floor(Math.random() * 15) + 5
    })

    // 下线打卡 (下午17:00-19:30)
    const checkoutHour = 17 + Math.floor(Math.random() * 3)
    const checkoutMinute = Math.floor(Math.random() * 60)
    const checkoutTime = new Date(date)
    checkoutTime.setHours(checkoutHour, checkoutMinute, Math.floor(Math.random() * 60), 0)

    // 有时候在同一个地点下线，有时候在不同地点
    const checkoutLocation = Math.random() > 0.3 ? checkinLocation : getRandomLocation()
    mockRecords.push({
      id: Date.now() + i * 100 + 2,
      type: 'checkout',
      time: checkoutTime,
      location: checkoutLocation,
      latitude: 39.9042 + (Math.random() - 0.5) * 0.02,
      longitude: 116.4074 + (Math.random() - 0.5) * 0.02,
      accuracy: Math.floor(Math.random() * 15) + 5
    })
  }

  // 按时间倒序排列
  checkinRecords.value = mockRecords.sort((a, b) => b.time - a.time)

  // 保存到本地存储
  uni.setStorageSync('checkinRecords', checkinRecords.value)

  console.log('生成了', checkinRecords.value.length, '条打卡记录')
}

// 获取随机位置
const getRandomLocation = () => {
  const locations = [
    '北京市朝阳区建国门外大街国贸大厦',
    '北京市海淀区中关村大街中关村广场',
    '北京市西城区西单北大街西单商场',
    '北京市东城区王府井大街王府井百货',
    '北京市丰台区南三环西路丰台科技园',
    '北京市石景山区石景山路万达广场',
    '北京市通州区新华西街通州万达',
    '北京市昌平区回龙观西大街龙德广场',
    '北京市大兴区黄村镇兴华大街',
    '北京市房山区良乡大学城',
    '北京市朝阳区三里屯太古里',
    '北京市海淀区五道口华清嘉园',
    '北京市西城区金融街购物中心',
    '北京市东城区东直门来福士',
    '北京市朝阳区望京SOHO',
    '北京市海淀区上地信息产业基地',
    '北京市丰台区方庄芳城园',
    '北京市石景山区鲁谷住宅小区',
    '北京市通州区果园环岛',
    '北京市昌平区天通苑北',
    '北京市大兴区亦庄经济开发区',
    '北京市房山区窦店镇',
    '北京市朝阳区CBD核心区',
    '北京市海淀区学院路',
    '北京市西城区新街口外大街'
  ]
  return locations[Math.floor(Math.random() * locations.length)]
}

// 加载工作统计
const loadWorkStats = async () => {
  try {
    // 这里应该调用真实的API获取工作统计
    // const stats = await taskApi.getWorkStats()

    // 基于打卡记录计算统计数据
    calculateWorkStats()
  } catch (error) {
    console.error('加载工作统计失败:', error)
  }
}

// 计算工作统计
const calculateWorkStats = () => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const weekStart = new Date(today)
  weekStart.setDate(today.getDate() - today.getDay())
  const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)

  // 过滤不同时间段的记录
  const todayRecords = checkinRecords.value.filter(record => {
    const recordDate = new Date(record.time)
    return recordDate >= today
  })

  const weekRecords = checkinRecords.value.filter(record => {
    const recordDate = new Date(record.time)
    return recordDate >= weekStart
  })

  const monthRecords = checkinRecords.value.filter(record => {
    const recordDate = new Date(record.time)
    return recordDate >= monthStart
  })

  // 计算任务数量（假设每次上线打卡对应处理任务）
  todayTaskCount.value = todayRecords.filter(r => r.type === 'checkin').length
  weekTaskCount.value = weekRecords.filter(r => r.type === 'checkin').length
  monthTaskCount.value = monthRecords.filter(r => r.type === 'checkin').length

  // 计算今日工作时长
  todayWorkTime.value = calculateWorkTime(todayRecords)

  // 计算总工作时长（基于本月数据）
  const monthWorkTime = calculateWorkTime(monthRecords)
  totalWorkTime.value = monthWorkTime
}

// 计算工作时长
const calculateWorkTime = (records) => {
  let totalMinutes = 0

  // 按日期分组
  const recordsByDate = {}
  records.forEach(record => {
    const dateKey = new Date(record.time).toDateString()
    if (!recordsByDate[dateKey]) {
      recordsByDate[dateKey] = []
    }
    recordsByDate[dateKey].push(record)
  })

  // 计算每天的工作时长
  Object.values(recordsByDate).forEach(dayRecords => {
    // 按时间排序
    dayRecords.sort((a, b) => new Date(a.time) - new Date(b.time))

    // 配对上线和下线记录
    for (let i = 0; i < dayRecords.length - 1; i += 2) {
      const checkin = dayRecords[i]
      const checkout = dayRecords[i + 1]

      if (checkin && checkout &&
          checkin.type === 'checkin' && checkout.type === 'checkout') {
        const diff = new Date(checkout.time) - new Date(checkin.time)
        totalMinutes += Math.floor(diff / (1000 * 60))
      }
    }
  })

  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60

  if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return '0分钟'
  }
}

// 刷新任务列表
const refreshTasks = async () => {
  if (isRefreshing.value) return

  try {
    isRefreshing.value = true

    // 模拟网络请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    await loadAvailableTasks()

    showToast('任务列表已刷新')
  } catch (error) {
    console.error('刷新任务失败:', error)
    showToast('刷新失败，请重试')
  } finally {
    isRefreshing.value = false
  }
}

// 接受任务
const acceptTask = async (task) => {
  try {
    // 这里应该调用真实的API接受任务
    // await taskApi.acceptTask(task.id)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    showToast('任务接受成功')
    currentTask.value = task
    loadAvailableTasks() // 重新加载可接任务
  } catch (error) {
    showToast('接受任务失败')
  }
}

// 查看任务详情
const viewTaskDetail = (task) => {
  uni.navigateTo({
    url: `/pages/task/task-detail?id=${task.id}`
  })
}

// 获取工作状态文本
const getStatusText = () => {
  return isOnline.value ? '工作中' : '已下线'
}

// 获取任务状态文本
const getTaskStatusText = (status) => {
  const statusMap = {
    new: '新任务',
    accepted: '已接收',
    progress: '进行中',
    completed: '已完成'
  }
  return statusMap[status] || status
}

// 获取任务类型文本
const getTaskTypeText = (type) => {
  const typeMap = {
    'install': '安装',
    'maintenance': '维保',
    'repair': '维修',
    'inspection': '巡检',
    'rectification': '整改',
    'delivery': '配送'
  }
  return typeMap[type] || '维保'
}

// 跳转到任务详情
const goToTaskDetail = () => {
  if (currentTask.value) {
    uni.navigateTo({
      url: `/pages/task/task-detail?id=${currentTask.value.id}`
    })
  }
}

// 开始作业
const startWork = () => {
  if (currentTask.value) {
    uni.navigateTo({
      url: `/pages/maintenance/work-process?taskId=${currentTask.value.id}`
    })
  }
}

// 格式化时间
const formatTime = (time) => {
  return formatRelativeTime(time)
}

// 查看所有记录
const viewAllRecords = () => {
  uni.navigateTo({
    url: '/pages/checkin/checkin-records'
  })
}

// 清除打卡记录（开发测试用）
const clearCheckinRecords = () => {
  uni.showModal({
    title: '确认清除',
    content: '确定要清除所有打卡记录吗？',
    success: (res) => {
      if (res.confirm) {
        checkinRecords.value = []
        uni.removeStorageSync('checkinRecords')
        uni.removeStorageSync('workStatus')
        isOnline.value = false
        workStartTime.value = null
        workTimeDisplay.value = '00:00:00'
        showToast('打卡记录已清除')

        // 重新计算统计数据
        calculateWorkStats()
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.checkin-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.status-bar {
  background: white;
  padding: $uni-spacing-base $uni-spacing-lg;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  gap: $uni-spacing-base;
}

.location-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8rpx;
  min-width: 0;
}

.refresh-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.refresh-btn:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

.location-text {
  font-size: $uni-font-size-sm;
  color: $uni-text-color;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-accuracy {
  font-size: $uni-font-size-xs;
  color: $uni-text-color-secondary;
  margin-left: 8rpx;
}

.status-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
  flex-shrink: 0;
}

.status-text {
  font-size: $uni-font-size-sm;
  font-weight: 500;

  &.online {
    color: $uni-color-success;
  }

  &.offline {
    color: $uni-text-color-secondary;
  }
}

.work-time {
  font-size: $uni-font-size-xs;
  color: $uni-text-color-secondary;
}

.checkin-main {
  padding: $uni-spacing-lg;
}

.checkin-card {
  background: white;
  border-radius: $uni-border-radius-lg;
  padding: $uni-spacing-2xl;
  box-shadow: $shadow-sm;
  text-align: center;
}

.checkin-time {
  margin-bottom: $uni-spacing-2xl;
}

.current-time {
  display: block;
  font-size: 80rpx;
  font-weight: 300;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-sm;
}

.current-date {
  font-size: $uni-font-size-base;
  color: $uni-text-color-secondary;
}

.checkin-button-area {
  margin: $uni-spacing-2xl 0;
}

.checkin-button {
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all 0.3s ease;

  &.online {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.3);
  }

  &.offline {
    background: linear-gradient(135deg, $uni-color-success, #38a169);
    box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
}

.button-icon {
  margin-bottom: $uni-spacing-sm;
}

.button-text {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: white;
}

.work-info {
  display: flex;
  justify-content: space-around;
  margin-top: $uni-spacing-2xl;
  padding-top: $uni-spacing-lg;
  border-top: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $uni-spacing-xs;
}

.info-label {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.info-value {
  font-size: $uni-font-size-base;
  font-weight: 500;
  color: $uni-text-color;

  &.online {
    color: $uni-color-success;
  }

  &.offline {
    color: $uni-text-color-secondary;
  }
}

.available-tasks, .current-task, .checkin-records, .work-stats {
  background: white;
  margin: 0 $uni-spacing-lg $uni-spacing-base;
  border-radius: $uni-border-radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $uni-spacing-lg;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title-with-icon {
  display: flex;
  align-items: center;
  gap: $uni-spacing-sm;
}

.section-title {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-text-color;
}

.more-btn {
  font-size: $uni-font-size-sm;
  color: $uni-color-primary;
}

// 任务刷新按钮
.task-refresh-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #4c6ef5 0%, #6c5ce7 100%);
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(76, 110, 245, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.task-refresh-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.task-refresh-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(76, 110, 245, 0.4);
}

.task-refresh-btn:active::before {
  left: 100%;
}

.refresh-text {
  font-size: $uni-font-size-sm;
  color: #fff;
  font-weight: 500;
}

// 刷新中状态
.task-refresh-btn.refreshing {
  background: linear-gradient(135deg, #a0a0a0 0%, #888 100%);
  pointer-events: none;
}

// 旋转动画
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.task-list {
  padding: $uni-spacing-lg;
}

.task-item {
  display: flex;
  align-items: center;
  padding: $uni-spacing-base 0;

  &:not(:last-child) {
    border-bottom: 1rpx solid #f0f0f0;
  }
}

.task-info {
  flex: 1;
}

.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.task-number {
  font-size: $uni-font-size-sm;
  color: $uni-color-primary;
  font-weight: 500;
  margin-right: 16rpx;
}

.task-type-tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: $uni-font-size-xs;

  &.type-install {
    background: #e8f5e8;
    color: #52c41a;
  }

  &.type-repair {
    background: #fff2e8;
    color: #fa8c16;
  }

  &.type-rectification {
    background: #fff1f0;
    color: #f5222d;
  }

  &.type-maintenance {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.type-inspection {
    background: #f6ffed;
    color: #389e0d;
  }
}

.task-customer {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  margin-bottom: 8rpx;
}

.task-address {
  display: flex;
  align-items: center;
}

.address-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4rpx;
  margin-right: 20rpx;
}

.address-text {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.task-distance {
  font-size: $uni-font-size-sm;
  color: $uni-color-primary;
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: $uni-spacing-base;
}

.accept-btn {
  padding: 8rpx 24rpx;
  background: $uni-color-primary;
  color: white;
  border: none;
  border-radius: $uni-border-radius-base;
  font-size: $uni-font-size-sm;
}

.task-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $uni-border-radius-base;
  font-size: $uni-font-size-base;
  border: none;

  &.primary {
    background: $uni-color-primary;
    color: white;
  }

  &.secondary {
    background: #f8f9fa;
    color: $uni-text-color;
  }
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: $uni-border-radius-lg;

  &.status-progress {
    background: rgba($uni-color-warning, 0.1);

    .status-text {
      color: $uni-color-warning;
    }
  }
}

.task-content {
  padding: $uni-spacing-lg;
}

.task-desc {
  font-size: $uni-font-size-lg;
  color: $uni-text-color;
  margin-bottom: $uni-spacing-xs;
}

.empty-tasks, .empty-records {
  text-align: center;
  padding: $uni-spacing-2xl;
}

.empty-icon {
  margin-bottom: $uni-spacing-base;
  opacity: 0.3;
}

.empty-text {
  font-size: $uni-font-size-base;
  color: $uni-text-color-secondary;
}

.record-list {
  padding: $uni-spacing-lg;
}

.record-item {
  display: flex;
  align-items: center;
  padding: $uni-spacing-base 0;

  &:not(:last-child) {
    border-bottom: 1rpx solid #f0f0f0;
  }
}

.record-content {
  width: 100%;
}

.record-title {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  margin-bottom: 4rpx;
}

.record-time {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  margin-bottom: 4rpx;
}

.record-location {
  display: flex;
  align-items: center;
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  gap: 4rpx;
}

.location-text {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

.record-status {
  width: 40rpx;
  text-align: center;
}

.status-dot {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;

  &.success {
    background: $uni-color-success;
  }
}

.stats-grid {
  display: flex;
  padding: $uni-spacing-lg;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $uni-spacing-xs;
}

.stat-value {
  font-size: $uni-font-size-xl;
  font-weight: 500;
  color: $uni-color-primary;
}

.stat-label {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
}

// 工作时长统计
.work-time-stats {
  padding: 0 $uni-spacing-lg $uni-spacing-lg;
  display: flex;
  gap: $uni-spacing-base;
}

.time-stat-item {
  flex: 1;
  background: #f8f9fa;
  border-radius: $uni-border-radius-base;
  padding: $uni-spacing-base;
}

.time-stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $uni-spacing-xs;
}

.time-stat-label {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-secondary;
  text-align: center;
}

.time-stat-value {
  font-size: $uni-font-size-lg;
  font-weight: 500;
  color: $uni-color-primary;
  text-align: center;
  line-height: 1.2;
}

// 底部安全区域
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
